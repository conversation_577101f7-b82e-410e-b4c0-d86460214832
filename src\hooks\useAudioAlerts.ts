import { useEffect, useRef } from 'react';
import { AlertType } from '@/types/market';

const createLoopingAudio = (src: string) => {
  const audio = new Audio(src);
  audio.loop = true;
  audio.preload = "auto";
  audio.volume = 0.65;
  return audio;
};

const tryWarmup = (audio?: HTMLAudioElement | null) => {
  if (!audio) return;
  void audio
    .play()
    .then(() => {
      audio.pause();
      audio.currentTime = 0;
    })
    .catch(() => {
      // Ignore gesture gating; playback will retry when triggered.
    });
};

export const useAudioAlerts = () => {
  const spreadAlertRef = useRef<HTMLAudioElement | null>(null);
  const deviationAlertRef = useRef<HTMLAudioElement | null>(null);
  const activeSpreadAlerts = useRef(new Set<string>());
  const activeDeviationAlerts = useRef(new Set<string>());

  useEffect(() => {
    spreadAlertRef.current = createLoopingAudio("/sounds/spread_alert.mp3");
    deviationAlertRef.current = createLoopingAudio("/sounds/deviation_alert.mp3");

    const unlockOnGesture = () => {
      tryWarmup(spreadAlertRef.current);
      tryWarmup(deviationAlertRef.current);
    document.removeEventListener('pointerdown', unlockOnGesture);
    document.removeEventListener('keydown', unlockOnGesture);
    };

    document.addEventListener('pointerdown', unlockOnGesture, { once: true });
    document.addEventListener('keydown', unlockOnGesture, { once: true });

    return () => {
      document.removeEventListener('pointerdown', unlockOnGesture);
      document.removeEventListener('keydown', unlockOnGesture);
      spreadAlertRef.current?.pause();
      deviationAlertRef.current?.pause();
      spreadAlertRef.current = null;
      deviationAlertRef.current = null;
      activeSpreadAlerts.current.clear();
      activeDeviationAlerts.current.clear();
    };
  }, []);

  const triggerAlert = (symbol: string, alertType: AlertType) => {
    if (alertType === 'SPREAD') {
      const wasEmpty = activeSpreadAlerts.current.size === 0;
      activeSpreadAlerts.current.add(symbol);

      if (wasEmpty && spreadAlertRef.current) {
        void spreadAlertRef.current.play().catch(() => {
          tryWarmup(spreadAlertRef.current);
          requestAnimationFrame(() => {
            void spreadAlertRef.current?.play().catch(() => {
              // Give up until the next user gesture.
            });
          });
        });
      }
    } else {
      const wasEmpty = activeDeviationAlerts.current.size === 0;
      activeDeviationAlerts.current.add(symbol);

      if (wasEmpty && deviationAlertRef.current) {
        void deviationAlertRef.current.play().catch(() => {
          tryWarmup(deviationAlertRef.current);
          requestAnimationFrame(() => {
            void deviationAlertRef.current?.play().catch(() => undefined);
          });
        });
      }
    }
  };

  const clearAlert = (symbol: string, alertType: AlertType) => {
    if (alertType === 'SPREAD') {
      activeSpreadAlerts.current.delete(symbol);
      if (activeSpreadAlerts.current.size === 0 && spreadAlertRef.current) {
        spreadAlertRef.current.pause();
        spreadAlertRef.current.currentTime = 0;
      }
    } else {
      activeDeviationAlerts.current.delete(symbol);
      if (activeDeviationAlerts.current.size === 0 && deviationAlertRef.current) {
        deviationAlertRef.current.pause();
        deviationAlertRef.current.currentTime = 0;
      }
    }
  };

  return { triggerAlert, clearAlert };
};
