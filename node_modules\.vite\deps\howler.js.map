{"version": 3, "sources": ["../../howler/dist/howler.js"], "sourcesContent": ["/*!\n *  howler.js v2.2.4\n *  howlerjs.com\n *\n *  (c) 2013-2020, <PERSON> of GoldFire Studios\n *  goldfirestudios.com\n *\n *  MIT License\n */\n\n(function() {\n\n  'use strict';\n\n  /** Global Methods **/\n  /***************************************************************************/\n\n  /**\n   * Create the global controller. All contained methods and properties apply\n   * to all sounds that are currently playing or will be in the future.\n   */\n  var HowlerGlobal = function() {\n    this.init();\n  };\n  HowlerGlobal.prototype = {\n    /**\n     * Initialize the global Howler object.\n     * @return {Howler}\n     */\n    init: function() {\n      var self = this || Howler;\n\n      // Create a global ID counter.\n      self._counter = 1000;\n\n      // Pool of unlocked HTML5 Audio objects.\n      self._html5AudioPool = [];\n      self.html5PoolSize = 10;\n\n      // Internal properties.\n      self._codecs = {};\n      self._howls = [];\n      self._muted = false;\n      self._volume = 1;\n      self._canPlayEvent = 'canplaythrough';\n      self._navigator = (typeof window !== 'undefined' && window.navigator) ? window.navigator : null;\n\n      // Public properties.\n      self.masterGain = null;\n      self.noAudio = false;\n      self.usingWebAudio = true;\n      self.autoSuspend = true;\n      self.ctx = null;\n\n      // Set to false to disable the auto audio unlocker.\n      self.autoUnlock = true;\n\n      // Setup the various state values for global tracking.\n      self._setup();\n\n      return self;\n    },\n\n    /**\n     * Get/set the global volume for all sounds.\n     * @param  {Float} vol Volume from 0.0 to 1.0.\n     * @return {Howler/Float}     Returns self or current volume.\n     */\n    volume: function(vol) {\n      var self = this || Howler;\n      vol = parseFloat(vol);\n\n      // If we don't have an AudioContext created yet, run the setup.\n      if (!self.ctx) {\n        setupAudioContext();\n      }\n\n      if (typeof vol !== 'undefined' && vol >= 0 && vol <= 1) {\n        self._volume = vol;\n\n        // Don't update any of the nodes if we are muted.\n        if (self._muted) {\n          return self;\n        }\n\n        // When using Web Audio, we just need to adjust the master gain.\n        if (self.usingWebAudio) {\n          self.masterGain.gain.setValueAtTime(vol, Howler.ctx.currentTime);\n        }\n\n        // Loop through and change volume for all HTML5 audio nodes.\n        for (var i=0; i<self._howls.length; i++) {\n          if (!self._howls[i]._webAudio) {\n            // Get all of the sounds in this Howl group.\n            var ids = self._howls[i]._getSoundIds();\n\n            // Loop through all sounds and change the volumes.\n            for (var j=0; j<ids.length; j++) {\n              var sound = self._howls[i]._soundById(ids[j]);\n\n              if (sound && sound._node) {\n                sound._node.volume = sound._volume * vol;\n              }\n            }\n          }\n        }\n\n        return self;\n      }\n\n      return self._volume;\n    },\n\n    /**\n     * Handle muting and unmuting globally.\n     * @param  {Boolean} muted Is muted or not.\n     */\n    mute: function(muted) {\n      var self = this || Howler;\n\n      // If we don't have an AudioContext created yet, run the setup.\n      if (!self.ctx) {\n        setupAudioContext();\n      }\n\n      self._muted = muted;\n\n      // With Web Audio, we just need to mute the master gain.\n      if (self.usingWebAudio) {\n        self.masterGain.gain.setValueAtTime(muted ? 0 : self._volume, Howler.ctx.currentTime);\n      }\n\n      // Loop through and mute all HTML5 Audio nodes.\n      for (var i=0; i<self._howls.length; i++) {\n        if (!self._howls[i]._webAudio) {\n          // Get all of the sounds in this Howl group.\n          var ids = self._howls[i]._getSoundIds();\n\n          // Loop through all sounds and mark the audio node as muted.\n          for (var j=0; j<ids.length; j++) {\n            var sound = self._howls[i]._soundById(ids[j]);\n\n            if (sound && sound._node) {\n              sound._node.muted = (muted) ? true : sound._muted;\n            }\n          }\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Handle stopping all sounds globally.\n     */\n    stop: function() {\n      var self = this || Howler;\n\n      // Loop through all Howls and stop them.\n      for (var i=0; i<self._howls.length; i++) {\n        self._howls[i].stop();\n      }\n\n      return self;\n    },\n\n    /**\n     * Unload and destroy all currently loaded Howl objects.\n     * @return {Howler}\n     */\n    unload: function() {\n      var self = this || Howler;\n\n      for (var i=self._howls.length-1; i>=0; i--) {\n        self._howls[i].unload();\n      }\n\n      // Create a new AudioContext to make sure it is fully reset.\n      if (self.usingWebAudio && self.ctx && typeof self.ctx.close !== 'undefined') {\n        self.ctx.close();\n        self.ctx = null;\n        setupAudioContext();\n      }\n\n      return self;\n    },\n\n    /**\n     * Check for codec support of specific extension.\n     * @param  {String} ext Audio file extention.\n     * @return {Boolean}\n     */\n    codecs: function(ext) {\n      return (this || Howler)._codecs[ext.replace(/^x-/, '')];\n    },\n\n    /**\n     * Setup various state values for global tracking.\n     * @return {Howler}\n     */\n    _setup: function() {\n      var self = this || Howler;\n\n      // Keeps track of the suspend/resume state of the AudioContext.\n      self.state = self.ctx ? self.ctx.state || 'suspended' : 'suspended';\n\n      // Automatically begin the 30-second suspend process\n      self._autoSuspend();\n\n      // Check if audio is available.\n      if (!self.usingWebAudio) {\n        // No audio is available on this system if noAudio is set to true.\n        if (typeof Audio !== 'undefined') {\n          try {\n            var test = new Audio();\n\n            // Check if the canplaythrough event is available.\n            if (typeof test.oncanplaythrough === 'undefined') {\n              self._canPlayEvent = 'canplay';\n            }\n          } catch(e) {\n            self.noAudio = true;\n          }\n        } else {\n          self.noAudio = true;\n        }\n      }\n\n      // Test to make sure audio isn't disabled in Internet Explorer.\n      try {\n        var test = new Audio();\n        if (test.muted) {\n          self.noAudio = true;\n        }\n      } catch (e) {}\n\n      // Check for supported codecs.\n      if (!self.noAudio) {\n        self._setupCodecs();\n      }\n\n      return self;\n    },\n\n    /**\n     * Check for browser support for various codecs and cache the results.\n     * @return {Howler}\n     */\n    _setupCodecs: function() {\n      var self = this || Howler;\n      var audioTest = null;\n\n      // Must wrap in a try/catch because IE11 in server mode throws an error.\n      try {\n        audioTest = (typeof Audio !== 'undefined') ? new Audio() : null;\n      } catch (err) {\n        return self;\n      }\n\n      if (!audioTest || typeof audioTest.canPlayType !== 'function') {\n        return self;\n      }\n\n      var mpegTest = audioTest.canPlayType('audio/mpeg;').replace(/^no$/, '');\n\n      // Opera version <33 has mixed MP3 support, so we need to check for and block it.\n      var ua = self._navigator ? self._navigator.userAgent : '';\n      var checkOpera = ua.match(/OPR\\/(\\d+)/g);\n      var isOldOpera = (checkOpera && parseInt(checkOpera[0].split('/')[1], 10) < 33);\n      var checkSafari = ua.indexOf('Safari') !== -1 && ua.indexOf('Chrome') === -1;\n      var safariVersion = ua.match(/Version\\/(.*?) /);\n      var isOldSafari = (checkSafari && safariVersion && parseInt(safariVersion[1], 10) < 15);\n\n      self._codecs = {\n        mp3: !!(!isOldOpera && (mpegTest || audioTest.canPlayType('audio/mp3;').replace(/^no$/, ''))),\n        mpeg: !!mpegTest,\n        opus: !!audioTest.canPlayType('audio/ogg; codecs=\"opus\"').replace(/^no$/, ''),\n        ogg: !!audioTest.canPlayType('audio/ogg; codecs=\"vorbis\"').replace(/^no$/, ''),\n        oga: !!audioTest.canPlayType('audio/ogg; codecs=\"vorbis\"').replace(/^no$/, ''),\n        wav: !!(audioTest.canPlayType('audio/wav; codecs=\"1\"') || audioTest.canPlayType('audio/wav')).replace(/^no$/, ''),\n        aac: !!audioTest.canPlayType('audio/aac;').replace(/^no$/, ''),\n        caf: !!audioTest.canPlayType('audio/x-caf;').replace(/^no$/, ''),\n        m4a: !!(audioTest.canPlayType('audio/x-m4a;') || audioTest.canPlayType('audio/m4a;') || audioTest.canPlayType('audio/aac;')).replace(/^no$/, ''),\n        m4b: !!(audioTest.canPlayType('audio/x-m4b;') || audioTest.canPlayType('audio/m4b;') || audioTest.canPlayType('audio/aac;')).replace(/^no$/, ''),\n        mp4: !!(audioTest.canPlayType('audio/x-mp4;') || audioTest.canPlayType('audio/mp4;') || audioTest.canPlayType('audio/aac;')).replace(/^no$/, ''),\n        weba: !!(!isOldSafari && audioTest.canPlayType('audio/webm; codecs=\"vorbis\"').replace(/^no$/, '')),\n        webm: !!(!isOldSafari && audioTest.canPlayType('audio/webm; codecs=\"vorbis\"').replace(/^no$/, '')),\n        dolby: !!audioTest.canPlayType('audio/mp4; codecs=\"ec-3\"').replace(/^no$/, ''),\n        flac: !!(audioTest.canPlayType('audio/x-flac;') || audioTest.canPlayType('audio/flac;')).replace(/^no$/, '')\n      };\n\n      return self;\n    },\n\n    /**\n     * Some browsers/devices will only allow audio to be played after a user interaction.\n     * Attempt to automatically unlock audio on the first user interaction.\n     * Concept from: http://paulbakaus.com/tutorials/html5/web-audio-on-ios/\n     * @return {Howler}\n     */\n    _unlockAudio: function() {\n      var self = this || Howler;\n\n      // Only run this if Web Audio is supported and it hasn't already been unlocked.\n      if (self._audioUnlocked || !self.ctx) {\n        return;\n      }\n\n      self._audioUnlocked = false;\n      self.autoUnlock = false;\n\n      // Some mobile devices/platforms have distortion issues when opening/closing tabs and/or web views.\n      // Bugs in the browser (especially Mobile Safari) can cause the sampleRate to change from 44100 to 48000.\n      // By calling Howler.unload(), we create a new AudioContext with the correct sampleRate.\n      if (!self._mobileUnloaded && self.ctx.sampleRate !== 44100) {\n        self._mobileUnloaded = true;\n        self.unload();\n      }\n\n      // Scratch buffer for enabling iOS to dispose of web audio buffers correctly, as per:\n      // http://stackoverflow.com/questions/24119684\n      self._scratchBuffer = self.ctx.createBuffer(1, 1, 22050);\n\n      // Call this method on touch start to create and play a buffer,\n      // then check if the audio actually played to determine if\n      // audio has now been unlocked on iOS, Android, etc.\n      var unlock = function(e) {\n        // Create a pool of unlocked HTML5 Audio objects that can\n        // be used for playing sounds without user interaction. HTML5\n        // Audio objects must be individually unlocked, as opposed\n        // to the WebAudio API which only needs a single activation.\n        // This must occur before WebAudio setup or the source.onended\n        // event will not fire.\n        while (self._html5AudioPool.length < self.html5PoolSize) {\n          try {\n            var audioNode = new Audio();\n\n            // Mark this Audio object as unlocked to ensure it can get returned\n            // to the unlocked pool when released.\n            audioNode._unlocked = true;\n\n            // Add the audio node to the pool.\n            self._releaseHtml5Audio(audioNode);\n          } catch (e) {\n            self.noAudio = true;\n            break;\n          }\n        }\n\n        // Loop through any assigned audio nodes and unlock them.\n        for (var i=0; i<self._howls.length; i++) {\n          if (!self._howls[i]._webAudio) {\n            // Get all of the sounds in this Howl group.\n            var ids = self._howls[i]._getSoundIds();\n\n            // Loop through all sounds and unlock the audio nodes.\n            for (var j=0; j<ids.length; j++) {\n              var sound = self._howls[i]._soundById(ids[j]);\n\n              if (sound && sound._node && !sound._node._unlocked) {\n                sound._node._unlocked = true;\n                sound._node.load();\n              }\n            }\n          }\n        }\n\n        // Fix Android can not play in suspend state.\n        self._autoResume();\n\n        // Create an empty buffer.\n        var source = self.ctx.createBufferSource();\n        source.buffer = self._scratchBuffer;\n        source.connect(self.ctx.destination);\n\n        // Play the empty buffer.\n        if (typeof source.start === 'undefined') {\n          source.noteOn(0);\n        } else {\n          source.start(0);\n        }\n\n        // Calling resume() on a stack initiated by user gesture is what actually unlocks the audio on Android Chrome >= 55.\n        if (typeof self.ctx.resume === 'function') {\n          self.ctx.resume();\n        }\n\n        // Setup a timeout to check that we are unlocked on the next event loop.\n        source.onended = function() {\n          source.disconnect(0);\n\n          // Update the unlocked state and prevent this check from happening again.\n          self._audioUnlocked = true;\n\n          // Remove the touch start listener.\n          document.removeEventListener('touchstart', unlock, true);\n          document.removeEventListener('touchend', unlock, true);\n          document.removeEventListener('click', unlock, true);\n          document.removeEventListener('keydown', unlock, true);\n\n          // Let all sounds know that audio has been unlocked.\n          for (var i=0; i<self._howls.length; i++) {\n            self._howls[i]._emit('unlock');\n          }\n        };\n      };\n\n      // Setup a touch start listener to attempt an unlock in.\n      document.addEventListener('touchstart', unlock, true);\n      document.addEventListener('touchend', unlock, true);\n      document.addEventListener('click', unlock, true);\n      document.addEventListener('keydown', unlock, true);\n\n      return self;\n    },\n\n    /**\n     * Get an unlocked HTML5 Audio object from the pool. If none are left,\n     * return a new Audio object and throw a warning.\n     * @return {Audio} HTML5 Audio object.\n     */\n    _obtainHtml5Audio: function() {\n      var self = this || Howler;\n\n      // Return the next object from the pool if one exists.\n      if (self._html5AudioPool.length) {\n        return self._html5AudioPool.pop();\n      }\n\n      //.Check if the audio is locked and throw a warning.\n      var testPlay = new Audio().play();\n      if (testPlay && typeof Promise !== 'undefined' && (testPlay instanceof Promise || typeof testPlay.then === 'function')) {\n        testPlay.catch(function() {\n          console.warn('HTML5 Audio pool exhausted, returning potentially locked audio object.');\n        });\n      }\n\n      return new Audio();\n    },\n\n    /**\n     * Return an activated HTML5 Audio object to the pool.\n     * @return {Howler}\n     */\n    _releaseHtml5Audio: function(audio) {\n      var self = this || Howler;\n\n      // Don't add audio to the pool if we don't know if it has been unlocked.\n      if (audio._unlocked) {\n        self._html5AudioPool.push(audio);\n      }\n\n      return self;\n    },\n\n    /**\n     * Automatically suspend the Web Audio AudioContext after no sound has played for 30 seconds.\n     * This saves processing/energy and fixes various browser-specific bugs with audio getting stuck.\n     * @return {Howler}\n     */\n    _autoSuspend: function() {\n      var self = this;\n\n      if (!self.autoSuspend || !self.ctx || typeof self.ctx.suspend === 'undefined' || !Howler.usingWebAudio) {\n        return;\n      }\n\n      // Check if any sounds are playing.\n      for (var i=0; i<self._howls.length; i++) {\n        if (self._howls[i]._webAudio) {\n          for (var j=0; j<self._howls[i]._sounds.length; j++) {\n            if (!self._howls[i]._sounds[j]._paused) {\n              return self;\n            }\n          }\n        }\n      }\n\n      if (self._suspendTimer) {\n        clearTimeout(self._suspendTimer);\n      }\n\n      // If no sound has played after 30 seconds, suspend the context.\n      self._suspendTimer = setTimeout(function() {\n        if (!self.autoSuspend) {\n          return;\n        }\n\n        self._suspendTimer = null;\n        self.state = 'suspending';\n\n        // Handle updating the state of the audio context after suspending.\n        var handleSuspension = function() {\n          self.state = 'suspended';\n\n          if (self._resumeAfterSuspend) {\n            delete self._resumeAfterSuspend;\n            self._autoResume();\n          }\n        };\n\n        // Either the state gets suspended or it is interrupted.\n        // Either way, we need to update the state to suspended.\n        self.ctx.suspend().then(handleSuspension, handleSuspension);\n      }, 30000);\n\n      return self;\n    },\n\n    /**\n     * Automatically resume the Web Audio AudioContext when a new sound is played.\n     * @return {Howler}\n     */\n    _autoResume: function() {\n      var self = this;\n\n      if (!self.ctx || typeof self.ctx.resume === 'undefined' || !Howler.usingWebAudio) {\n        return;\n      }\n\n      if (self.state === 'running' && self.ctx.state !== 'interrupted' && self._suspendTimer) {\n        clearTimeout(self._suspendTimer);\n        self._suspendTimer = null;\n      } else if (self.state === 'suspended' || self.state === 'running' && self.ctx.state === 'interrupted') {\n        self.ctx.resume().then(function() {\n          self.state = 'running';\n\n          // Emit to all Howls that the audio has resumed.\n          for (var i=0; i<self._howls.length; i++) {\n            self._howls[i]._emit('resume');\n          }\n        });\n\n        if (self._suspendTimer) {\n          clearTimeout(self._suspendTimer);\n          self._suspendTimer = null;\n        }\n      } else if (self.state === 'suspending') {\n        self._resumeAfterSuspend = true;\n      }\n\n      return self;\n    }\n  };\n\n  // Setup the global audio controller.\n  var Howler = new HowlerGlobal();\n\n  /** Group Methods **/\n  /***************************************************************************/\n\n  /**\n   * Create an audio group controller.\n   * @param {Object} o Passed in properties for this group.\n   */\n  var Howl = function(o) {\n    var self = this;\n\n    // Throw an error if no source is provided.\n    if (!o.src || o.src.length === 0) {\n      console.error('An array of source files must be passed with any new Howl.');\n      return;\n    }\n\n    self.init(o);\n  };\n  Howl.prototype = {\n    /**\n     * Initialize a new Howl group object.\n     * @param  {Object} o Passed in properties for this group.\n     * @return {Howl}\n     */\n    init: function(o) {\n      var self = this;\n\n      // If we don't have an AudioContext created yet, run the setup.\n      if (!Howler.ctx) {\n        setupAudioContext();\n      }\n\n      // Setup user-defined default properties.\n      self._autoplay = o.autoplay || false;\n      self._format = (typeof o.format !== 'string') ? o.format : [o.format];\n      self._html5 = o.html5 || false;\n      self._muted = o.mute || false;\n      self._loop = o.loop || false;\n      self._pool = o.pool || 5;\n      self._preload = (typeof o.preload === 'boolean' || o.preload === 'metadata') ? o.preload : true;\n      self._rate = o.rate || 1;\n      self._sprite = o.sprite || {};\n      self._src = (typeof o.src !== 'string') ? o.src : [o.src];\n      self._volume = o.volume !== undefined ? o.volume : 1;\n      self._xhr = {\n        method: o.xhr && o.xhr.method ? o.xhr.method : 'GET',\n        headers: o.xhr && o.xhr.headers ? o.xhr.headers : null,\n        withCredentials: o.xhr && o.xhr.withCredentials ? o.xhr.withCredentials : false,\n      };\n\n      // Setup all other default properties.\n      self._duration = 0;\n      self._state = 'unloaded';\n      self._sounds = [];\n      self._endTimers = {};\n      self._queue = [];\n      self._playLock = false;\n\n      // Setup event listeners.\n      self._onend = o.onend ? [{fn: o.onend}] : [];\n      self._onfade = o.onfade ? [{fn: o.onfade}] : [];\n      self._onload = o.onload ? [{fn: o.onload}] : [];\n      self._onloaderror = o.onloaderror ? [{fn: o.onloaderror}] : [];\n      self._onplayerror = o.onplayerror ? [{fn: o.onplayerror}] : [];\n      self._onpause = o.onpause ? [{fn: o.onpause}] : [];\n      self._onplay = o.onplay ? [{fn: o.onplay}] : [];\n      self._onstop = o.onstop ? [{fn: o.onstop}] : [];\n      self._onmute = o.onmute ? [{fn: o.onmute}] : [];\n      self._onvolume = o.onvolume ? [{fn: o.onvolume}] : [];\n      self._onrate = o.onrate ? [{fn: o.onrate}] : [];\n      self._onseek = o.onseek ? [{fn: o.onseek}] : [];\n      self._onunlock = o.onunlock ? [{fn: o.onunlock}] : [];\n      self._onresume = [];\n\n      // Web Audio or HTML5 Audio?\n      self._webAudio = Howler.usingWebAudio && !self._html5;\n\n      // Automatically try to enable audio.\n      if (typeof Howler.ctx !== 'undefined' && Howler.ctx && Howler.autoUnlock) {\n        Howler._unlockAudio();\n      }\n\n      // Keep track of this Howl group in the global controller.\n      Howler._howls.push(self);\n\n      // If they selected autoplay, add a play event to the load queue.\n      if (self._autoplay) {\n        self._queue.push({\n          event: 'play',\n          action: function() {\n            self.play();\n          }\n        });\n      }\n\n      // Load the source file unless otherwise specified.\n      if (self._preload && self._preload !== 'none') {\n        self.load();\n      }\n\n      return self;\n    },\n\n    /**\n     * Load the audio file.\n     * @return {Howler}\n     */\n    load: function() {\n      var self = this;\n      var url = null;\n\n      // If no audio is available, quit immediately.\n      if (Howler.noAudio) {\n        self._emit('loaderror', null, 'No audio support.');\n        return;\n      }\n\n      // Make sure our source is in an array.\n      if (typeof self._src === 'string') {\n        self._src = [self._src];\n      }\n\n      // Loop through the sources and pick the first one that is compatible.\n      for (var i=0; i<self._src.length; i++) {\n        var ext, str;\n\n        if (self._format && self._format[i]) {\n          // If an extension was specified, use that instead.\n          ext = self._format[i];\n        } else {\n          // Make sure the source is a string.\n          str = self._src[i];\n          if (typeof str !== 'string') {\n            self._emit('loaderror', null, 'Non-string found in selected audio sources - ignoring.');\n            continue;\n          }\n\n          // Extract the file extension from the URL or base64 data URI.\n          ext = /^data:audio\\/([^;,]+);/i.exec(str);\n          if (!ext) {\n            ext = /\\.([^.]+)$/.exec(str.split('?', 1)[0]);\n          }\n\n          if (ext) {\n            ext = ext[1].toLowerCase();\n          }\n        }\n\n        // Log a warning if no extension was found.\n        if (!ext) {\n          console.warn('No file extension was found. Consider using the \"format\" property or specify an extension.');\n        }\n\n        // Check if this extension is available.\n        if (ext && Howler.codecs(ext)) {\n          url = self._src[i];\n          break;\n        }\n      }\n\n      if (!url) {\n        self._emit('loaderror', null, 'No codec support for selected audio sources.');\n        return;\n      }\n\n      self._src = url;\n      self._state = 'loading';\n\n      // If the hosting page is HTTPS and the source isn't,\n      // drop down to HTML5 Audio to avoid Mixed Content errors.\n      if (window.location.protocol === 'https:' && url.slice(0, 5) === 'http:') {\n        self._html5 = true;\n        self._webAudio = false;\n      }\n\n      // Create a new sound object and add it to the pool.\n      new Sound(self);\n\n      // Load and decode the audio data for playback.\n      if (self._webAudio) {\n        loadBuffer(self);\n      }\n\n      return self;\n    },\n\n    /**\n     * Play a sound or resume previous playback.\n     * @param  {String/Number} sprite   Sprite name for sprite playback or sound id to continue previous.\n     * @param  {Boolean} internal Internal Use: true prevents event firing.\n     * @return {Number}          Sound ID.\n     */\n    play: function(sprite, internal) {\n      var self = this;\n      var id = null;\n\n      // Determine if a sprite, sound id or nothing was passed\n      if (typeof sprite === 'number') {\n        id = sprite;\n        sprite = null;\n      } else if (typeof sprite === 'string' && self._state === 'loaded' && !self._sprite[sprite]) {\n        // If the passed sprite doesn't exist, do nothing.\n        return null;\n      } else if (typeof sprite === 'undefined') {\n        // Use the default sound sprite (plays the full audio length).\n        sprite = '__default';\n\n        // Check if there is a single paused sound that isn't ended.\n        // If there is, play that sound. If not, continue as usual.\n        if (!self._playLock) {\n          var num = 0;\n          for (var i=0; i<self._sounds.length; i++) {\n            if (self._sounds[i]._paused && !self._sounds[i]._ended) {\n              num++;\n              id = self._sounds[i]._id;\n            }\n          }\n\n          if (num === 1) {\n            sprite = null;\n          } else {\n            id = null;\n          }\n        }\n      }\n\n      // Get the selected node, or get one from the pool.\n      var sound = id ? self._soundById(id) : self._inactiveSound();\n\n      // If the sound doesn't exist, do nothing.\n      if (!sound) {\n        return null;\n      }\n\n      // Select the sprite definition.\n      if (id && !sprite) {\n        sprite = sound._sprite || '__default';\n      }\n\n      // If the sound hasn't loaded, we must wait to get the audio's duration.\n      // We also need to wait to make sure we don't run into race conditions with\n      // the order of function calls.\n      if (self._state !== 'loaded') {\n        // Set the sprite value on this sound.\n        sound._sprite = sprite;\n\n        // Mark this sound as not ended in case another sound is played before this one loads.\n        sound._ended = false;\n\n        // Add the sound to the queue to be played on load.\n        var soundId = sound._id;\n        self._queue.push({\n          event: 'play',\n          action: function() {\n            self.play(soundId);\n          }\n        });\n\n        return soundId;\n      }\n\n      // Don't play the sound if an id was passed and it is already playing.\n      if (id && !sound._paused) {\n        // Trigger the play event, in order to keep iterating through queue.\n        if (!internal) {\n          self._loadQueue('play');\n        }\n\n        return sound._id;\n      }\n\n      // Make sure the AudioContext isn't suspended, and resume it if it is.\n      if (self._webAudio) {\n        Howler._autoResume();\n      }\n\n      // Determine how long to play for and where to start playing.\n      var seek = Math.max(0, sound._seek > 0 ? sound._seek : self._sprite[sprite][0] / 1000);\n      var duration = Math.max(0, ((self._sprite[sprite][0] + self._sprite[sprite][1]) / 1000) - seek);\n      var timeout = (duration * 1000) / Math.abs(sound._rate);\n      var start = self._sprite[sprite][0] / 1000;\n      var stop = (self._sprite[sprite][0] + self._sprite[sprite][1]) / 1000;\n      sound._sprite = sprite;\n\n      // Mark the sound as ended instantly so that this async playback\n      // doesn't get grabbed by another call to play while this one waits to start.\n      sound._ended = false;\n\n      // Update the parameters of the sound.\n      var setParams = function() {\n        sound._paused = false;\n        sound._seek = seek;\n        sound._start = start;\n        sound._stop = stop;\n        sound._loop = !!(sound._loop || self._sprite[sprite][2]);\n      };\n\n      // End the sound instantly if seek is at the end.\n      if (seek >= stop) {\n        self._ended(sound);\n        return;\n      }\n\n      // Begin the actual playback.\n      var node = sound._node;\n      if (self._webAudio) {\n        // Fire this when the sound is ready to play to begin Web Audio playback.\n        var playWebAudio = function() {\n          self._playLock = false;\n          setParams();\n          self._refreshBuffer(sound);\n\n          // Setup the playback params.\n          var vol = (sound._muted || self._muted) ? 0 : sound._volume;\n          node.gain.setValueAtTime(vol, Howler.ctx.currentTime);\n          sound._playStart = Howler.ctx.currentTime;\n\n          // Play the sound using the supported method.\n          if (typeof node.bufferSource.start === 'undefined') {\n            sound._loop ? node.bufferSource.noteGrainOn(0, seek, 86400) : node.bufferSource.noteGrainOn(0, seek, duration);\n          } else {\n            sound._loop ? node.bufferSource.start(0, seek, 86400) : node.bufferSource.start(0, seek, duration);\n          }\n\n          // Start a new timer if none is present.\n          if (timeout !== Infinity) {\n            self._endTimers[sound._id] = setTimeout(self._ended.bind(self, sound), timeout);\n          }\n\n          if (!internal) {\n            setTimeout(function() {\n              self._emit('play', sound._id);\n              self._loadQueue();\n            }, 0);\n          }\n        };\n\n        if (Howler.state === 'running' && Howler.ctx.state !== 'interrupted') {\n          playWebAudio();\n        } else {\n          self._playLock = true;\n\n          // Wait for the audio context to resume before playing.\n          self.once('resume', playWebAudio);\n\n          // Cancel the end timer.\n          self._clearTimer(sound._id);\n        }\n      } else {\n        // Fire this when the sound is ready to play to begin HTML5 Audio playback.\n        var playHtml5 = function() {\n          node.currentTime = seek;\n          node.muted = sound._muted || self._muted || Howler._muted || node.muted;\n          node.volume = sound._volume * Howler.volume();\n          node.playbackRate = sound._rate;\n\n          // Some browsers will throw an error if this is called without user interaction.\n          try {\n            var play = node.play();\n\n            // Support older browsers that don't support promises, and thus don't have this issue.\n            if (play && typeof Promise !== 'undefined' && (play instanceof Promise || typeof play.then === 'function')) {\n              // Implements a lock to prevent DOMException: The play() request was interrupted by a call to pause().\n              self._playLock = true;\n\n              // Set param values immediately.\n              setParams();\n\n              // Releases the lock and executes queued actions.\n              play\n                .then(function() {\n                  self._playLock = false;\n                  node._unlocked = true;\n                  if (!internal) {\n                    self._emit('play', sound._id);\n                  } else {\n                    self._loadQueue();\n                  }\n                })\n                .catch(function() {\n                  self._playLock = false;\n                  self._emit('playerror', sound._id, 'Playback was unable to start. This is most commonly an issue ' +\n                    'on mobile devices and Chrome where playback was not within a user interaction.');\n\n                  // Reset the ended and paused values.\n                  sound._ended = true;\n                  sound._paused = true;\n                });\n            } else if (!internal) {\n              self._playLock = false;\n              setParams();\n              self._emit('play', sound._id);\n            }\n\n            // Setting rate before playing won't work in IE, so we set it again here.\n            node.playbackRate = sound._rate;\n\n            // If the node is still paused, then we can assume there was a playback issue.\n            if (node.paused) {\n              self._emit('playerror', sound._id, 'Playback was unable to start. This is most commonly an issue ' +\n                'on mobile devices and Chrome where playback was not within a user interaction.');\n              return;\n            }\n\n            // Setup the end timer on sprites or listen for the ended event.\n            if (sprite !== '__default' || sound._loop) {\n              self._endTimers[sound._id] = setTimeout(self._ended.bind(self, sound), timeout);\n            } else {\n              self._endTimers[sound._id] = function() {\n                // Fire ended on this audio node.\n                self._ended(sound);\n\n                // Clear this listener.\n                node.removeEventListener('ended', self._endTimers[sound._id], false);\n              };\n              node.addEventListener('ended', self._endTimers[sound._id], false);\n            }\n          } catch (err) {\n            self._emit('playerror', sound._id, err);\n          }\n        };\n\n        // If this is streaming audio, make sure the src is set and load again.\n        if (node.src === 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA') {\n          node.src = self._src;\n          node.load();\n        }\n\n        // Play immediately if ready, or wait for the 'canplaythrough'e vent.\n        var loadedNoReadyState = (window && window.ejecta) || (!node.readyState && Howler._navigator.isCocoonJS);\n        if (node.readyState >= 3 || loadedNoReadyState) {\n          playHtml5();\n        } else {\n          self._playLock = true;\n          self._state = 'loading';\n\n          var listener = function() {\n            self._state = 'loaded';\n            \n            // Begin playback.\n            playHtml5();\n\n            // Clear this listener.\n            node.removeEventListener(Howler._canPlayEvent, listener, false);\n          };\n          node.addEventListener(Howler._canPlayEvent, listener, false);\n\n          // Cancel the end timer.\n          self._clearTimer(sound._id);\n        }\n      }\n\n      return sound._id;\n    },\n\n    /**\n     * Pause playback and save current position.\n     * @param  {Number} id The sound ID (empty to pause all in group).\n     * @return {Howl}\n     */\n    pause: function(id) {\n      var self = this;\n\n      // If the sound hasn't loaded or a play() promise is pending, add it to the load queue to pause when capable.\n      if (self._state !== 'loaded' || self._playLock) {\n        self._queue.push({\n          event: 'pause',\n          action: function() {\n            self.pause(id);\n          }\n        });\n\n        return self;\n      }\n\n      // If no id is passed, get all ID's to be paused.\n      var ids = self._getSoundIds(id);\n\n      for (var i=0; i<ids.length; i++) {\n        // Clear the end timer.\n        self._clearTimer(ids[i]);\n\n        // Get the sound.\n        var sound = self._soundById(ids[i]);\n\n        if (sound && !sound._paused) {\n          // Reset the seek position.\n          sound._seek = self.seek(ids[i]);\n          sound._rateSeek = 0;\n          sound._paused = true;\n\n          // Stop currently running fades.\n          self._stopFade(ids[i]);\n\n          if (sound._node) {\n            if (self._webAudio) {\n              // Make sure the sound has been created.\n              if (!sound._node.bufferSource) {\n                continue;\n              }\n\n              if (typeof sound._node.bufferSource.stop === 'undefined') {\n                sound._node.bufferSource.noteOff(0);\n              } else {\n                sound._node.bufferSource.stop(0);\n              }\n\n              // Clean up the buffer source.\n              self._cleanBuffer(sound._node);\n            } else if (!isNaN(sound._node.duration) || sound._node.duration === Infinity) {\n              sound._node.pause();\n            }\n          }\n        }\n\n        // Fire the pause event, unless `true` is passed as the 2nd argument.\n        if (!arguments[1]) {\n          self._emit('pause', sound ? sound._id : null);\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Stop playback and reset to start.\n     * @param  {Number} id The sound ID (empty to stop all in group).\n     * @param  {Boolean} internal Internal Use: true prevents event firing.\n     * @return {Howl}\n     */\n    stop: function(id, internal) {\n      var self = this;\n\n      // If the sound hasn't loaded, add it to the load queue to stop when capable.\n      if (self._state !== 'loaded' || self._playLock) {\n        self._queue.push({\n          event: 'stop',\n          action: function() {\n            self.stop(id);\n          }\n        });\n\n        return self;\n      }\n\n      // If no id is passed, get all ID's to be stopped.\n      var ids = self._getSoundIds(id);\n\n      for (var i=0; i<ids.length; i++) {\n        // Clear the end timer.\n        self._clearTimer(ids[i]);\n\n        // Get the sound.\n        var sound = self._soundById(ids[i]);\n\n        if (sound) {\n          // Reset the seek position.\n          sound._seek = sound._start || 0;\n          sound._rateSeek = 0;\n          sound._paused = true;\n          sound._ended = true;\n\n          // Stop currently running fades.\n          self._stopFade(ids[i]);\n\n          if (sound._node) {\n            if (self._webAudio) {\n              // Make sure the sound's AudioBufferSourceNode has been created.\n              if (sound._node.bufferSource) {\n                if (typeof sound._node.bufferSource.stop === 'undefined') {\n                  sound._node.bufferSource.noteOff(0);\n                } else {\n                  sound._node.bufferSource.stop(0);\n                }\n\n                // Clean up the buffer source.\n                self._cleanBuffer(sound._node);\n              }\n            } else if (!isNaN(sound._node.duration) || sound._node.duration === Infinity) {\n              sound._node.currentTime = sound._start || 0;\n              sound._node.pause();\n\n              // If this is a live stream, stop download once the audio is stopped.\n              if (sound._node.duration === Infinity) {\n                self._clearSound(sound._node);\n              }\n            }\n          }\n\n          if (!internal) {\n            self._emit('stop', sound._id);\n          }\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Mute/unmute a single sound or all sounds in this Howl group.\n     * @param  {Boolean} muted Set to true to mute and false to unmute.\n     * @param  {Number} id    The sound ID to update (omit to mute/unmute all).\n     * @return {Howl}\n     */\n    mute: function(muted, id) {\n      var self = this;\n\n      // If the sound hasn't loaded, add it to the load queue to mute when capable.\n      if (self._state !== 'loaded'|| self._playLock) {\n        self._queue.push({\n          event: 'mute',\n          action: function() {\n            self.mute(muted, id);\n          }\n        });\n\n        return self;\n      }\n\n      // If applying mute/unmute to all sounds, update the group's value.\n      if (typeof id === 'undefined') {\n        if (typeof muted === 'boolean') {\n          self._muted = muted;\n        } else {\n          return self._muted;\n        }\n      }\n\n      // If no id is passed, get all ID's to be muted.\n      var ids = self._getSoundIds(id);\n\n      for (var i=0; i<ids.length; i++) {\n        // Get the sound.\n        var sound = self._soundById(ids[i]);\n\n        if (sound) {\n          sound._muted = muted;\n\n          // Cancel active fade and set the volume to the end value.\n          if (sound._interval) {\n            self._stopFade(sound._id);\n          }\n\n          if (self._webAudio && sound._node) {\n            sound._node.gain.setValueAtTime(muted ? 0 : sound._volume, Howler.ctx.currentTime);\n          } else if (sound._node) {\n            sound._node.muted = Howler._muted ? true : muted;\n          }\n\n          self._emit('mute', sound._id);\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Get/set the volume of this sound or of the Howl group. This method can optionally take 0, 1 or 2 arguments.\n     *   volume() -> Returns the group's volume value.\n     *   volume(id) -> Returns the sound id's current volume.\n     *   volume(vol) -> Sets the volume of all sounds in this Howl group.\n     *   volume(vol, id) -> Sets the volume of passed sound id.\n     * @return {Howl/Number} Returns self or current volume.\n     */\n    volume: function() {\n      var self = this;\n      var args = arguments;\n      var vol, id;\n\n      // Determine the values based on arguments.\n      if (args.length === 0) {\n        // Return the value of the groups' volume.\n        return self._volume;\n      } else if (args.length === 1 || args.length === 2 && typeof args[1] === 'undefined') {\n        // First check if this is an ID, and if not, assume it is a new volume.\n        var ids = self._getSoundIds();\n        var index = ids.indexOf(args[0]);\n        if (index >= 0) {\n          id = parseInt(args[0], 10);\n        } else {\n          vol = parseFloat(args[0]);\n        }\n      } else if (args.length >= 2) {\n        vol = parseFloat(args[0]);\n        id = parseInt(args[1], 10);\n      }\n\n      // Update the volume or return the current volume.\n      var sound;\n      if (typeof vol !== 'undefined' && vol >= 0 && vol <= 1) {\n        // If the sound hasn't loaded, add it to the load queue to change volume when capable.\n        if (self._state !== 'loaded'|| self._playLock) {\n          self._queue.push({\n            event: 'volume',\n            action: function() {\n              self.volume.apply(self, args);\n            }\n          });\n\n          return self;\n        }\n\n        // Set the group volume.\n        if (typeof id === 'undefined') {\n          self._volume = vol;\n        }\n\n        // Update one or all volumes.\n        id = self._getSoundIds(id);\n        for (var i=0; i<id.length; i++) {\n          // Get the sound.\n          sound = self._soundById(id[i]);\n\n          if (sound) {\n            sound._volume = vol;\n\n            // Stop currently running fades.\n            if (!args[2]) {\n              self._stopFade(id[i]);\n            }\n\n            if (self._webAudio && sound._node && !sound._muted) {\n              sound._node.gain.setValueAtTime(vol, Howler.ctx.currentTime);\n            } else if (sound._node && !sound._muted) {\n              sound._node.volume = vol * Howler.volume();\n            }\n\n            self._emit('volume', sound._id);\n          }\n        }\n      } else {\n        sound = id ? self._soundById(id) : self._sounds[0];\n        return sound ? sound._volume : 0;\n      }\n\n      return self;\n    },\n\n    /**\n     * Fade a currently playing sound between two volumes (if no id is passed, all sounds will fade).\n     * @param  {Number} from The value to fade from (0.0 to 1.0).\n     * @param  {Number} to   The volume to fade to (0.0 to 1.0).\n     * @param  {Number} len  Time in milliseconds to fade.\n     * @param  {Number} id   The sound id (omit to fade all sounds).\n     * @return {Howl}\n     */\n    fade: function(from, to, len, id) {\n      var self = this;\n\n      // If the sound hasn't loaded, add it to the load queue to fade when capable.\n      if (self._state !== 'loaded' || self._playLock) {\n        self._queue.push({\n          event: 'fade',\n          action: function() {\n            self.fade(from, to, len, id);\n          }\n        });\n\n        return self;\n      }\n\n      // Make sure the to/from/len values are numbers.\n      from = Math.min(Math.max(0, parseFloat(from)), 1);\n      to = Math.min(Math.max(0, parseFloat(to)), 1);\n      len = parseFloat(len);\n\n      // Set the volume to the start position.\n      self.volume(from, id);\n\n      // Fade the volume of one or all sounds.\n      var ids = self._getSoundIds(id);\n      for (var i=0; i<ids.length; i++) {\n        // Get the sound.\n        var sound = self._soundById(ids[i]);\n\n        // Create a linear fade or fall back to timeouts with HTML5 Audio.\n        if (sound) {\n          // Stop the previous fade if no sprite is being used (otherwise, volume handles this).\n          if (!id) {\n            self._stopFade(ids[i]);\n          }\n\n          // If we are using Web Audio, let the native methods do the actual fade.\n          if (self._webAudio && !sound._muted) {\n            var currentTime = Howler.ctx.currentTime;\n            var end = currentTime + (len / 1000);\n            sound._volume = from;\n            sound._node.gain.setValueAtTime(from, currentTime);\n            sound._node.gain.linearRampToValueAtTime(to, end);\n          }\n\n          self._startFadeInterval(sound, from, to, len, ids[i], typeof id === 'undefined');\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Starts the internal interval to fade a sound.\n     * @param  {Object} sound Reference to sound to fade.\n     * @param  {Number} from The value to fade from (0.0 to 1.0).\n     * @param  {Number} to   The volume to fade to (0.0 to 1.0).\n     * @param  {Number} len  Time in milliseconds to fade.\n     * @param  {Number} id   The sound id to fade.\n     * @param  {Boolean} isGroup   If true, set the volume on the group.\n     */\n    _startFadeInterval: function(sound, from, to, len, id, isGroup) {\n      var self = this;\n      var vol = from;\n      var diff = to - from;\n      var steps = Math.abs(diff / 0.01);\n      var stepLen = Math.max(4, (steps > 0) ? len / steps : len);\n      var lastTick = Date.now();\n\n      // Store the value being faded to.\n      sound._fadeTo = to;\n\n      // Update the volume value on each interval tick.\n      sound._interval = setInterval(function() {\n        // Update the volume based on the time since the last tick.\n        var tick = (Date.now() - lastTick) / len;\n        lastTick = Date.now();\n        vol += diff * tick;\n\n        // Round to within 2 decimal points.\n        vol = Math.round(vol * 100) / 100;\n\n        // Make sure the volume is in the right bounds.\n        if (diff < 0) {\n          vol = Math.max(to, vol);\n        } else {\n          vol = Math.min(to, vol);\n        }\n\n        // Change the volume.\n        if (self._webAudio) {\n          sound._volume = vol;\n        } else {\n          self.volume(vol, sound._id, true);\n        }\n\n        // Set the group's volume.\n        if (isGroup) {\n          self._volume = vol;\n        }\n\n        // When the fade is complete, stop it and fire event.\n        if ((to < from && vol <= to) || (to > from && vol >= to)) {\n          clearInterval(sound._interval);\n          sound._interval = null;\n          sound._fadeTo = null;\n          self.volume(to, sound._id);\n          self._emit('fade', sound._id);\n        }\n      }, stepLen);\n    },\n\n    /**\n     * Internal method that stops the currently playing fade when\n     * a new fade starts, volume is changed or the sound is stopped.\n     * @param  {Number} id The sound id.\n     * @return {Howl}\n     */\n    _stopFade: function(id) {\n      var self = this;\n      var sound = self._soundById(id);\n\n      if (sound && sound._interval) {\n        if (self._webAudio) {\n          sound._node.gain.cancelScheduledValues(Howler.ctx.currentTime);\n        }\n\n        clearInterval(sound._interval);\n        sound._interval = null;\n        self.volume(sound._fadeTo, id);\n        sound._fadeTo = null;\n        self._emit('fade', id);\n      }\n\n      return self;\n    },\n\n    /**\n     * Get/set the loop parameter on a sound. This method can optionally take 0, 1 or 2 arguments.\n     *   loop() -> Returns the group's loop value.\n     *   loop(id) -> Returns the sound id's loop value.\n     *   loop(loop) -> Sets the loop value for all sounds in this Howl group.\n     *   loop(loop, id) -> Sets the loop value of passed sound id.\n     * @return {Howl/Boolean} Returns self or current loop value.\n     */\n    loop: function() {\n      var self = this;\n      var args = arguments;\n      var loop, id, sound;\n\n      // Determine the values for loop and id.\n      if (args.length === 0) {\n        // Return the grou's loop value.\n        return self._loop;\n      } else if (args.length === 1) {\n        if (typeof args[0] === 'boolean') {\n          loop = args[0];\n          self._loop = loop;\n        } else {\n          // Return this sound's loop value.\n          sound = self._soundById(parseInt(args[0], 10));\n          return sound ? sound._loop : false;\n        }\n      } else if (args.length === 2) {\n        loop = args[0];\n        id = parseInt(args[1], 10);\n      }\n\n      // If no id is passed, get all ID's to be looped.\n      var ids = self._getSoundIds(id);\n      for (var i=0; i<ids.length; i++) {\n        sound = self._soundById(ids[i]);\n\n        if (sound) {\n          sound._loop = loop;\n          if (self._webAudio && sound._node && sound._node.bufferSource) {\n            sound._node.bufferSource.loop = loop;\n            if (loop) {\n              sound._node.bufferSource.loopStart = sound._start || 0;\n              sound._node.bufferSource.loopEnd = sound._stop;\n\n              // If playing, restart playback to ensure looping updates.\n              if (self.playing(ids[i])) {\n                self.pause(ids[i], true);\n                self.play(ids[i], true);\n              }\n            }\n          }\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Get/set the playback rate of a sound. This method can optionally take 0, 1 or 2 arguments.\n     *   rate() -> Returns the first sound node's current playback rate.\n     *   rate(id) -> Returns the sound id's current playback rate.\n     *   rate(rate) -> Sets the playback rate of all sounds in this Howl group.\n     *   rate(rate, id) -> Sets the playback rate of passed sound id.\n     * @return {Howl/Number} Returns self or the current playback rate.\n     */\n    rate: function() {\n      var self = this;\n      var args = arguments;\n      var rate, id;\n\n      // Determine the values based on arguments.\n      if (args.length === 0) {\n        // We will simply return the current rate of the first node.\n        id = self._sounds[0]._id;\n      } else if (args.length === 1) {\n        // First check if this is an ID, and if not, assume it is a new rate value.\n        var ids = self._getSoundIds();\n        var index = ids.indexOf(args[0]);\n        if (index >= 0) {\n          id = parseInt(args[0], 10);\n        } else {\n          rate = parseFloat(args[0]);\n        }\n      } else if (args.length === 2) {\n        rate = parseFloat(args[0]);\n        id = parseInt(args[1], 10);\n      }\n\n      // Update the playback rate or return the current value.\n      var sound;\n      if (typeof rate === 'number') {\n        // If the sound hasn't loaded, add it to the load queue to change playback rate when capable.\n        if (self._state !== 'loaded' || self._playLock) {\n          self._queue.push({\n            event: 'rate',\n            action: function() {\n              self.rate.apply(self, args);\n            }\n          });\n\n          return self;\n        }\n\n        // Set the group rate.\n        if (typeof id === 'undefined') {\n          self._rate = rate;\n        }\n\n        // Update one or all volumes.\n        id = self._getSoundIds(id);\n        for (var i=0; i<id.length; i++) {\n          // Get the sound.\n          sound = self._soundById(id[i]);\n\n          if (sound) {\n            // Keep track of our position when the rate changed and update the playback\n            // start position so we can properly adjust the seek position for time elapsed.\n            if (self.playing(id[i])) {\n              sound._rateSeek = self.seek(id[i]);\n              sound._playStart = self._webAudio ? Howler.ctx.currentTime : sound._playStart;\n            }\n            sound._rate = rate;\n\n            // Change the playback rate.\n            if (self._webAudio && sound._node && sound._node.bufferSource) {\n              sound._node.bufferSource.playbackRate.setValueAtTime(rate, Howler.ctx.currentTime);\n            } else if (sound._node) {\n              sound._node.playbackRate = rate;\n            }\n\n            // Reset the timers.\n            var seek = self.seek(id[i]);\n            var duration = ((self._sprite[sound._sprite][0] + self._sprite[sound._sprite][1]) / 1000) - seek;\n            var timeout = (duration * 1000) / Math.abs(sound._rate);\n\n            // Start a new end timer if sound is already playing.\n            if (self._endTimers[id[i]] || !sound._paused) {\n              self._clearTimer(id[i]);\n              self._endTimers[id[i]] = setTimeout(self._ended.bind(self, sound), timeout);\n            }\n\n            self._emit('rate', sound._id);\n          }\n        }\n      } else {\n        sound = self._soundById(id);\n        return sound ? sound._rate : self._rate;\n      }\n\n      return self;\n    },\n\n    /**\n     * Get/set the seek position of a sound. This method can optionally take 0, 1 or 2 arguments.\n     *   seek() -> Returns the first sound node's current seek position.\n     *   seek(id) -> Returns the sound id's current seek position.\n     *   seek(seek) -> Sets the seek position of the first sound node.\n     *   seek(seek, id) -> Sets the seek position of passed sound id.\n     * @return {Howl/Number} Returns self or the current seek position.\n     */\n    seek: function() {\n      var self = this;\n      var args = arguments;\n      var seek, id;\n\n      // Determine the values based on arguments.\n      if (args.length === 0) {\n        // We will simply return the current position of the first node.\n        if (self._sounds.length) {\n          id = self._sounds[0]._id;\n        }\n      } else if (args.length === 1) {\n        // First check if this is an ID, and if not, assume it is a new seek position.\n        var ids = self._getSoundIds();\n        var index = ids.indexOf(args[0]);\n        if (index >= 0) {\n          id = parseInt(args[0], 10);\n        } else if (self._sounds.length) {\n          id = self._sounds[0]._id;\n          seek = parseFloat(args[0]);\n        }\n      } else if (args.length === 2) {\n        seek = parseFloat(args[0]);\n        id = parseInt(args[1], 10);\n      }\n\n      // If there is no ID, bail out.\n      if (typeof id === 'undefined') {\n        return 0;\n      }\n\n      // If the sound hasn't loaded, add it to the load queue to seek when capable.\n      if (typeof seek === 'number' && (self._state !== 'loaded' || self._playLock)) {\n        self._queue.push({\n          event: 'seek',\n          action: function() {\n            self.seek.apply(self, args);\n          }\n        });\n\n        return self;\n      }\n\n      // Get the sound.\n      var sound = self._soundById(id);\n\n      if (sound) {\n        if (typeof seek === 'number' && seek >= 0) {\n          // Pause the sound and update position for restarting playback.\n          var playing = self.playing(id);\n          if (playing) {\n            self.pause(id, true);\n          }\n\n          // Move the position of the track and cancel timer.\n          sound._seek = seek;\n          sound._ended = false;\n          self._clearTimer(id);\n\n          // Update the seek position for HTML5 Audio.\n          if (!self._webAudio && sound._node && !isNaN(sound._node.duration)) {\n            sound._node.currentTime = seek;\n          }\n\n          // Seek and emit when ready.\n          var seekAndEmit = function() {\n            // Restart the playback if the sound was playing.\n            if (playing) {\n              self.play(id, true);\n            }\n\n            self._emit('seek', id);\n          };\n\n          // Wait for the play lock to be unset before emitting (HTML5 Audio).\n          if (playing && !self._webAudio) {\n            var emitSeek = function() {\n              if (!self._playLock) {\n                seekAndEmit();\n              } else {\n                setTimeout(emitSeek, 0);\n              }\n            };\n            setTimeout(emitSeek, 0);\n          } else {\n            seekAndEmit();\n          }\n        } else {\n          if (self._webAudio) {\n            var realTime = self.playing(id) ? Howler.ctx.currentTime - sound._playStart : 0;\n            var rateSeek = sound._rateSeek ? sound._rateSeek - sound._seek : 0;\n            return sound._seek + (rateSeek + realTime * Math.abs(sound._rate));\n          } else {\n            return sound._node.currentTime;\n          }\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Check if a specific sound is currently playing or not (if id is provided), or check if at least one of the sounds in the group is playing or not.\n     * @param  {Number}  id The sound id to check. If none is passed, the whole sound group is checked.\n     * @return {Boolean} True if playing and false if not.\n     */\n    playing: function(id) {\n      var self = this;\n\n      // Check the passed sound ID (if any).\n      if (typeof id === 'number') {\n        var sound = self._soundById(id);\n        return sound ? !sound._paused : false;\n      }\n\n      // Otherwise, loop through all sounds and check if any are playing.\n      for (var i=0; i<self._sounds.length; i++) {\n        if (!self._sounds[i]._paused) {\n          return true;\n        }\n      }\n\n      return false;\n    },\n\n    /**\n     * Get the duration of this sound. Passing a sound id will return the sprite duration.\n     * @param  {Number} id The sound id to check. If none is passed, return full source duration.\n     * @return {Number} Audio duration in seconds.\n     */\n    duration: function(id) {\n      var self = this;\n      var duration = self._duration;\n\n      // If we pass an ID, get the sound and return the sprite length.\n      var sound = self._soundById(id);\n      if (sound) {\n        duration = self._sprite[sound._sprite][1] / 1000;\n      }\n\n      return duration;\n    },\n\n    /**\n     * Returns the current loaded state of this Howl.\n     * @return {String} 'unloaded', 'loading', 'loaded'\n     */\n    state: function() {\n      return this._state;\n    },\n\n    /**\n     * Unload and destroy the current Howl object.\n     * This will immediately stop all sound instances attached to this group.\n     */\n    unload: function() {\n      var self = this;\n\n      // Stop playing any active sounds.\n      var sounds = self._sounds;\n      for (var i=0; i<sounds.length; i++) {\n        // Stop the sound if it is currently playing.\n        if (!sounds[i]._paused) {\n          self.stop(sounds[i]._id);\n        }\n\n        // Remove the source or disconnect.\n        if (!self._webAudio) {\n          // Set the source to 0-second silence to stop any downloading (except in IE).\n          self._clearSound(sounds[i]._node);\n\n          // Remove any event listeners.\n          sounds[i]._node.removeEventListener('error', sounds[i]._errorFn, false);\n          sounds[i]._node.removeEventListener(Howler._canPlayEvent, sounds[i]._loadFn, false);\n          sounds[i]._node.removeEventListener('ended', sounds[i]._endFn, false);\n\n          // Release the Audio object back to the pool.\n          Howler._releaseHtml5Audio(sounds[i]._node);\n        }\n\n        // Empty out all of the nodes.\n        delete sounds[i]._node;\n\n        // Make sure all timers are cleared out.\n        self._clearTimer(sounds[i]._id);\n      }\n\n      // Remove the references in the global Howler object.\n      var index = Howler._howls.indexOf(self);\n      if (index >= 0) {\n        Howler._howls.splice(index, 1);\n      }\n\n      // Delete this sound from the cache (if no other Howl is using it).\n      var remCache = true;\n      for (i=0; i<Howler._howls.length; i++) {\n        if (Howler._howls[i]._src === self._src || self._src.indexOf(Howler._howls[i]._src) >= 0) {\n          remCache = false;\n          break;\n        }\n      }\n\n      if (cache && remCache) {\n        delete cache[self._src];\n      }\n\n      // Clear global errors.\n      Howler.noAudio = false;\n\n      // Clear out `self`.\n      self._state = 'unloaded';\n      self._sounds = [];\n      self = null;\n\n      return null;\n    },\n\n    /**\n     * Listen to a custom event.\n     * @param  {String}   event Event name.\n     * @param  {Function} fn    Listener to call.\n     * @param  {Number}   id    (optional) Only listen to events for this sound.\n     * @param  {Number}   once  (INTERNAL) Marks event to fire only once.\n     * @return {Howl}\n     */\n    on: function(event, fn, id, once) {\n      var self = this;\n      var events = self['_on' + event];\n\n      if (typeof fn === 'function') {\n        events.push(once ? {id: id, fn: fn, once: once} : {id: id, fn: fn});\n      }\n\n      return self;\n    },\n\n    /**\n     * Remove a custom event. Call without parameters to remove all events.\n     * @param  {String}   event Event name.\n     * @param  {Function} fn    Listener to remove. Leave empty to remove all.\n     * @param  {Number}   id    (optional) Only remove events for this sound.\n     * @return {Howl}\n     */\n    off: function(event, fn, id) {\n      var self = this;\n      var events = self['_on' + event];\n      var i = 0;\n\n      // Allow passing just an event and ID.\n      if (typeof fn === 'number') {\n        id = fn;\n        fn = null;\n      }\n\n      if (fn || id) {\n        // Loop through event store and remove the passed function.\n        for (i=0; i<events.length; i++) {\n          var isId = (id === events[i].id);\n          if (fn === events[i].fn && isId || !fn && isId) {\n            events.splice(i, 1);\n            break;\n          }\n        }\n      } else if (event) {\n        // Clear out all events of this type.\n        self['_on' + event] = [];\n      } else {\n        // Clear out all events of every type.\n        var keys = Object.keys(self);\n        for (i=0; i<keys.length; i++) {\n          if ((keys[i].indexOf('_on') === 0) && Array.isArray(self[keys[i]])) {\n            self[keys[i]] = [];\n          }\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Listen to a custom event and remove it once fired.\n     * @param  {String}   event Event name.\n     * @param  {Function} fn    Listener to call.\n     * @param  {Number}   id    (optional) Only listen to events for this sound.\n     * @return {Howl}\n     */\n    once: function(event, fn, id) {\n      var self = this;\n\n      // Setup the event listener.\n      self.on(event, fn, id, 1);\n\n      return self;\n    },\n\n    /**\n     * Emit all events of a specific type and pass the sound id.\n     * @param  {String} event Event name.\n     * @param  {Number} id    Sound ID.\n     * @param  {Number} msg   Message to go with event.\n     * @return {Howl}\n     */\n    _emit: function(event, id, msg) {\n      var self = this;\n      var events = self['_on' + event];\n\n      // Loop through event store and fire all functions.\n      for (var i=events.length-1; i>=0; i--) {\n        // Only fire the listener if the correct ID is used.\n        if (!events[i].id || events[i].id === id || event === 'load') {\n          setTimeout(function(fn) {\n            fn.call(this, id, msg);\n          }.bind(self, events[i].fn), 0);\n\n          // If this event was setup with `once`, remove it.\n          if (events[i].once) {\n            self.off(event, events[i].fn, events[i].id);\n          }\n        }\n      }\n\n      // Pass the event type into load queue so that it can continue stepping.\n      self._loadQueue(event);\n\n      return self;\n    },\n\n    /**\n     * Queue of actions initiated before the sound has loaded.\n     * These will be called in sequence, with the next only firing\n     * after the previous has finished executing (even if async like play).\n     * @return {Howl}\n     */\n    _loadQueue: function(event) {\n      var self = this;\n\n      if (self._queue.length > 0) {\n        var task = self._queue[0];\n\n        // Remove this task if a matching event was passed.\n        if (task.event === event) {\n          self._queue.shift();\n          self._loadQueue();\n        }\n\n        // Run the task if no event type is passed.\n        if (!event) {\n          task.action();\n        }\n      }\n\n      return self;\n    },\n\n    /**\n     * Fired when playback ends at the end of the duration.\n     * @param  {Sound} sound The sound object to work with.\n     * @return {Howl}\n     */\n    _ended: function(sound) {\n      var self = this;\n      var sprite = sound._sprite;\n\n      // If we are using IE and there was network latency we may be clipping\n      // audio before it completes playing. Lets check the node to make sure it\n      // believes it has completed, before ending the playback.\n      if (!self._webAudio && sound._node && !sound._node.paused && !sound._node.ended && sound._node.currentTime < sound._stop) {\n        setTimeout(self._ended.bind(self, sound), 100);\n        return self;\n      }\n\n      // Should this sound loop?\n      var loop = !!(sound._loop || self._sprite[sprite][2]);\n\n      // Fire the ended event.\n      self._emit('end', sound._id);\n\n      // Restart the playback for HTML5 Audio loop.\n      if (!self._webAudio && loop) {\n        self.stop(sound._id, true).play(sound._id);\n      }\n\n      // Restart this timer if on a Web Audio loop.\n      if (self._webAudio && loop) {\n        self._emit('play', sound._id);\n        sound._seek = sound._start || 0;\n        sound._rateSeek = 0;\n        sound._playStart = Howler.ctx.currentTime;\n\n        var timeout = ((sound._stop - sound._start) * 1000) / Math.abs(sound._rate);\n        self._endTimers[sound._id] = setTimeout(self._ended.bind(self, sound), timeout);\n      }\n\n      // Mark the node as paused.\n      if (self._webAudio && !loop) {\n        sound._paused = true;\n        sound._ended = true;\n        sound._seek = sound._start || 0;\n        sound._rateSeek = 0;\n        self._clearTimer(sound._id);\n\n        // Clean up the buffer source.\n        self._cleanBuffer(sound._node);\n\n        // Attempt to auto-suspend AudioContext if no sounds are still playing.\n        Howler._autoSuspend();\n      }\n\n      // When using a sprite, end the track.\n      if (!self._webAudio && !loop) {\n        self.stop(sound._id, true);\n      }\n\n      return self;\n    },\n\n    /**\n     * Clear the end timer for a sound playback.\n     * @param  {Number} id The sound ID.\n     * @return {Howl}\n     */\n    _clearTimer: function(id) {\n      var self = this;\n\n      if (self._endTimers[id]) {\n        // Clear the timeout or remove the ended listener.\n        if (typeof self._endTimers[id] !== 'function') {\n          clearTimeout(self._endTimers[id]);\n        } else {\n          var sound = self._soundById(id);\n          if (sound && sound._node) {\n            sound._node.removeEventListener('ended', self._endTimers[id], false);\n          }\n        }\n\n        delete self._endTimers[id];\n      }\n\n      return self;\n    },\n\n    /**\n     * Return the sound identified by this ID, or return null.\n     * @param  {Number} id Sound ID\n     * @return {Object}    Sound object or null.\n     */\n    _soundById: function(id) {\n      var self = this;\n\n      // Loop through all sounds and find the one with this ID.\n      for (var i=0; i<self._sounds.length; i++) {\n        if (id === self._sounds[i]._id) {\n          return self._sounds[i];\n        }\n      }\n\n      return null;\n    },\n\n    /**\n     * Return an inactive sound from the pool or create a new one.\n     * @return {Sound} Sound playback object.\n     */\n    _inactiveSound: function() {\n      var self = this;\n\n      self._drain();\n\n      // Find the first inactive node to recycle.\n      for (var i=0; i<self._sounds.length; i++) {\n        if (self._sounds[i]._ended) {\n          return self._sounds[i].reset();\n        }\n      }\n\n      // If no inactive node was found, create a new one.\n      return new Sound(self);\n    },\n\n    /**\n     * Drain excess inactive sounds from the pool.\n     */\n    _drain: function() {\n      var self = this;\n      var limit = self._pool;\n      var cnt = 0;\n      var i = 0;\n\n      // If there are less sounds than the max pool size, we are done.\n      if (self._sounds.length < limit) {\n        return;\n      }\n\n      // Count the number of inactive sounds.\n      for (i=0; i<self._sounds.length; i++) {\n        if (self._sounds[i]._ended) {\n          cnt++;\n        }\n      }\n\n      // Remove excess inactive sounds, going in reverse order.\n      for (i=self._sounds.length - 1; i>=0; i--) {\n        if (cnt <= limit) {\n          return;\n        }\n\n        if (self._sounds[i]._ended) {\n          // Disconnect the audio source when using Web Audio.\n          if (self._webAudio && self._sounds[i]._node) {\n            self._sounds[i]._node.disconnect(0);\n          }\n\n          // Remove sounds until we have the pool size.\n          self._sounds.splice(i, 1);\n          cnt--;\n        }\n      }\n    },\n\n    /**\n     * Get all ID's from the sounds pool.\n     * @param  {Number} id Only return one ID if one is passed.\n     * @return {Array}    Array of IDs.\n     */\n    _getSoundIds: function(id) {\n      var self = this;\n\n      if (typeof id === 'undefined') {\n        var ids = [];\n        for (var i=0; i<self._sounds.length; i++) {\n          ids.push(self._sounds[i]._id);\n        }\n\n        return ids;\n      } else {\n        return [id];\n      }\n    },\n\n    /**\n     * Load the sound back into the buffer source.\n     * @param  {Sound} sound The sound object to work with.\n     * @return {Howl}\n     */\n    _refreshBuffer: function(sound) {\n      var self = this;\n\n      // Setup the buffer source for playback.\n      sound._node.bufferSource = Howler.ctx.createBufferSource();\n      sound._node.bufferSource.buffer = cache[self._src];\n\n      // Connect to the correct node.\n      if (sound._panner) {\n        sound._node.bufferSource.connect(sound._panner);\n      } else {\n        sound._node.bufferSource.connect(sound._node);\n      }\n\n      // Setup looping and playback rate.\n      sound._node.bufferSource.loop = sound._loop;\n      if (sound._loop) {\n        sound._node.bufferSource.loopStart = sound._start || 0;\n        sound._node.bufferSource.loopEnd = sound._stop || 0;\n      }\n      sound._node.bufferSource.playbackRate.setValueAtTime(sound._rate, Howler.ctx.currentTime);\n\n      return self;\n    },\n\n    /**\n     * Prevent memory leaks by cleaning up the buffer source after playback.\n     * @param  {Object} node Sound's audio node containing the buffer source.\n     * @return {Howl}\n     */\n    _cleanBuffer: function(node) {\n      var self = this;\n      var isIOS = Howler._navigator && Howler._navigator.vendor.indexOf('Apple') >= 0;\n\n      if (!node.bufferSource) {\n        return self;\n      }\n\n      if (Howler._scratchBuffer && node.bufferSource) {\n        node.bufferSource.onended = null;\n        node.bufferSource.disconnect(0);\n        if (isIOS) {\n          try { node.bufferSource.buffer = Howler._scratchBuffer; } catch(e) {}\n        }\n      }\n      node.bufferSource = null;\n\n      return self;\n    },\n\n    /**\n     * Set the source to a 0-second silence to stop any downloading (except in IE).\n     * @param  {Object} node Audio node to clear.\n     */\n    _clearSound: function(node) {\n      var checkIE = /MSIE |Trident\\//.test(Howler._navigator && Howler._navigator.userAgent);\n      if (!checkIE) {\n        node.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA';\n      }\n    }\n  };\n\n  /** Single Sound Methods **/\n  /***************************************************************************/\n\n  /**\n   * Setup the sound object, which each node attached to a Howl group is contained in.\n   * @param {Object} howl The Howl parent group.\n   */\n  var Sound = function(howl) {\n    this._parent = howl;\n    this.init();\n  };\n  Sound.prototype = {\n    /**\n     * Initialize a new Sound object.\n     * @return {Sound}\n     */\n    init: function() {\n      var self = this;\n      var parent = self._parent;\n\n      // Setup the default parameters.\n      self._muted = parent._muted;\n      self._loop = parent._loop;\n      self._volume = parent._volume;\n      self._rate = parent._rate;\n      self._seek = 0;\n      self._paused = true;\n      self._ended = true;\n      self._sprite = '__default';\n\n      // Generate a unique ID for this sound.\n      self._id = ++Howler._counter;\n\n      // Add itself to the parent's pool.\n      parent._sounds.push(self);\n\n      // Create the new node.\n      self.create();\n\n      return self;\n    },\n\n    /**\n     * Create and setup a new sound object, whether HTML5 Audio or Web Audio.\n     * @return {Sound}\n     */\n    create: function() {\n      var self = this;\n      var parent = self._parent;\n      var volume = (Howler._muted || self._muted || self._parent._muted) ? 0 : self._volume;\n\n      if (parent._webAudio) {\n        // Create the gain node for controlling volume (the source will connect to this).\n        self._node = (typeof Howler.ctx.createGain === 'undefined') ? Howler.ctx.createGainNode() : Howler.ctx.createGain();\n        self._node.gain.setValueAtTime(volume, Howler.ctx.currentTime);\n        self._node.paused = true;\n        self._node.connect(Howler.masterGain);\n      } else if (!Howler.noAudio) {\n        // Get an unlocked Audio object from the pool.\n        self._node = Howler._obtainHtml5Audio();\n\n        // Listen for errors (http://dev.w3.org/html5/spec-author-view/spec.html#mediaerror).\n        self._errorFn = self._errorListener.bind(self);\n        self._node.addEventListener('error', self._errorFn, false);\n\n        // Listen for 'canplaythrough' event to let us know the sound is ready.\n        self._loadFn = self._loadListener.bind(self);\n        self._node.addEventListener(Howler._canPlayEvent, self._loadFn, false);\n\n        // Listen for the 'ended' event on the sound to account for edge-case where\n        // a finite sound has a duration of Infinity.\n        self._endFn = self._endListener.bind(self);\n        self._node.addEventListener('ended', self._endFn, false);\n\n        // Setup the new audio node.\n        self._node.src = parent._src;\n        self._node.preload = parent._preload === true ? 'auto' : parent._preload;\n        self._node.volume = volume * Howler.volume();\n\n        // Begin loading the source.\n        self._node.load();\n      }\n\n      return self;\n    },\n\n    /**\n     * Reset the parameters of this sound to the original state (for recycle).\n     * @return {Sound}\n     */\n    reset: function() {\n      var self = this;\n      var parent = self._parent;\n\n      // Reset all of the parameters of this sound.\n      self._muted = parent._muted;\n      self._loop = parent._loop;\n      self._volume = parent._volume;\n      self._rate = parent._rate;\n      self._seek = 0;\n      self._rateSeek = 0;\n      self._paused = true;\n      self._ended = true;\n      self._sprite = '__default';\n\n      // Generate a new ID so that it isn't confused with the previous sound.\n      self._id = ++Howler._counter;\n\n      return self;\n    },\n\n    /**\n     * HTML5 Audio error listener callback.\n     */\n    _errorListener: function() {\n      var self = this;\n\n      // Fire an error event and pass back the code.\n      self._parent._emit('loaderror', self._id, self._node.error ? self._node.error.code : 0);\n\n      // Clear the event listener.\n      self._node.removeEventListener('error', self._errorFn, false);\n    },\n\n    /**\n     * HTML5 Audio canplaythrough listener callback.\n     */\n    _loadListener: function() {\n      var self = this;\n      var parent = self._parent;\n\n      // Round up the duration to account for the lower precision in HTML5 Audio.\n      parent._duration = Math.ceil(self._node.duration * 10) / 10;\n\n      // Setup a sprite if none is defined.\n      if (Object.keys(parent._sprite).length === 0) {\n        parent._sprite = {__default: [0, parent._duration * 1000]};\n      }\n\n      if (parent._state !== 'loaded') {\n        parent._state = 'loaded';\n        parent._emit('load');\n        parent._loadQueue();\n      }\n\n      // Clear the event listener.\n      self._node.removeEventListener(Howler._canPlayEvent, self._loadFn, false);\n    },\n\n    /**\n     * HTML5 Audio ended listener callback.\n     */\n    _endListener: function() {\n      var self = this;\n      var parent = self._parent;\n\n      // Only handle the `ended`` event if the duration is Infinity.\n      if (parent._duration === Infinity) {\n        // Update the parent duration to match the real audio duration.\n        // Round up the duration to account for the lower precision in HTML5 Audio.\n        parent._duration = Math.ceil(self._node.duration * 10) / 10;\n\n        // Update the sprite that corresponds to the real duration.\n        if (parent._sprite.__default[1] === Infinity) {\n          parent._sprite.__default[1] = parent._duration * 1000;\n        }\n\n        // Run the regular ended method.\n        parent._ended(self);\n      }\n\n      // Clear the event listener since the duration is now correct.\n      self._node.removeEventListener('ended', self._endFn, false);\n    }\n  };\n\n  /** Helper Methods **/\n  /***************************************************************************/\n\n  var cache = {};\n\n  /**\n   * Buffer a sound from URL, Data URI or cache and decode to audio source (Web Audio API).\n   * @param  {Howl} self\n   */\n  var loadBuffer = function(self) {\n    var url = self._src;\n\n    // Check if the buffer has already been cached and use it instead.\n    if (cache[url]) {\n      // Set the duration from the cache.\n      self._duration = cache[url].duration;\n\n      // Load the sound into this Howl.\n      loadSound(self);\n\n      return;\n    }\n\n    if (/^data:[^;]+;base64,/.test(url)) {\n      // Decode the base64 data URI without XHR, since some browsers don't support it.\n      var data = atob(url.split(',')[1]);\n      var dataView = new Uint8Array(data.length);\n      for (var i=0; i<data.length; ++i) {\n        dataView[i] = data.charCodeAt(i);\n      }\n\n      decodeAudioData(dataView.buffer, self);\n    } else {\n      // Load the buffer from the URL.\n      var xhr = new XMLHttpRequest();\n      xhr.open(self._xhr.method, url, true);\n      xhr.withCredentials = self._xhr.withCredentials;\n      xhr.responseType = 'arraybuffer';\n\n      // Apply any custom headers to the request.\n      if (self._xhr.headers) {\n        Object.keys(self._xhr.headers).forEach(function(key) {\n          xhr.setRequestHeader(key, self._xhr.headers[key]);\n        });\n      }\n\n      xhr.onload = function() {\n        // Make sure we get a successful response back.\n        var code = (xhr.status + '')[0];\n        if (code !== '0' && code !== '2' && code !== '3') {\n          self._emit('loaderror', null, 'Failed loading audio file with status: ' + xhr.status + '.');\n          return;\n        }\n\n        decodeAudioData(xhr.response, self);\n      };\n      xhr.onerror = function() {\n        // If there is an error, switch to HTML5 Audio.\n        if (self._webAudio) {\n          self._html5 = true;\n          self._webAudio = false;\n          self._sounds = [];\n          delete cache[url];\n          self.load();\n        }\n      };\n      safeXhrSend(xhr);\n    }\n  };\n\n  /**\n   * Send the XHR request wrapped in a try/catch.\n   * @param  {Object} xhr XHR to send.\n   */\n  var safeXhrSend = function(xhr) {\n    try {\n      xhr.send();\n    } catch (e) {\n      xhr.onerror();\n    }\n  };\n\n  /**\n   * Decode audio data from an array buffer.\n   * @param  {ArrayBuffer} arraybuffer The audio data.\n   * @param  {Howl}        self\n   */\n  var decodeAudioData = function(arraybuffer, self) {\n    // Fire a load error if something broke.\n    var error = function() {\n      self._emit('loaderror', null, 'Decoding audio data failed.');\n    };\n\n    // Load the sound on success.\n    var success = function(buffer) {\n      if (buffer && self._sounds.length > 0) {\n        cache[self._src] = buffer;\n        loadSound(self, buffer);\n      } else {\n        error();\n      }\n    };\n\n    // Decode the buffer into an audio source.\n    if (typeof Promise !== 'undefined' && Howler.ctx.decodeAudioData.length === 1) {\n      Howler.ctx.decodeAudioData(arraybuffer).then(success).catch(error);\n    } else {\n      Howler.ctx.decodeAudioData(arraybuffer, success, error);\n    }\n  }\n\n  /**\n   * Sound is now loaded, so finish setting everything up and fire the loaded event.\n   * @param  {Howl} self\n   * @param  {Object} buffer The decoded buffer sound source.\n   */\n  var loadSound = function(self, buffer) {\n    // Set the duration.\n    if (buffer && !self._duration) {\n      self._duration = buffer.duration;\n    }\n\n    // Setup a sprite if none is defined.\n    if (Object.keys(self._sprite).length === 0) {\n      self._sprite = {__default: [0, self._duration * 1000]};\n    }\n\n    // Fire the loaded event.\n    if (self._state !== 'loaded') {\n      self._state = 'loaded';\n      self._emit('load');\n      self._loadQueue();\n    }\n  };\n\n  /**\n   * Setup the audio context when available, or switch to HTML5 Audio mode.\n   */\n  var setupAudioContext = function() {\n    // If we have already detected that Web Audio isn't supported, don't run this step again.\n    if (!Howler.usingWebAudio) {\n      return;\n    }\n\n    // Check if we are using Web Audio and setup the AudioContext if we are.\n    try {\n      if (typeof AudioContext !== 'undefined') {\n        Howler.ctx = new AudioContext();\n      } else if (typeof webkitAudioContext !== 'undefined') {\n        Howler.ctx = new webkitAudioContext();\n      } else {\n        Howler.usingWebAudio = false;\n      }\n    } catch(e) {\n      Howler.usingWebAudio = false;\n    }\n\n    // If the audio context creation still failed, set using web audio to false.\n    if (!Howler.ctx) {\n      Howler.usingWebAudio = false;\n    }\n\n    // Check if a webview is being used on iOS8 or earlier (rather than the browser).\n    // If it is, disable Web Audio as it causes crashing.\n    var iOS = (/iP(hone|od|ad)/.test(Howler._navigator && Howler._navigator.platform));\n    var appVersion = Howler._navigator && Howler._navigator.appVersion.match(/OS (\\d+)_(\\d+)_?(\\d+)?/);\n    var version = appVersion ? parseInt(appVersion[1], 10) : null;\n    if (iOS && version && version < 9) {\n      var safari = /safari/.test(Howler._navigator && Howler._navigator.userAgent.toLowerCase());\n      if (Howler._navigator && !safari) {\n        Howler.usingWebAudio = false;\n      }\n    }\n\n    // Create and expose the master GainNode when using Web Audio (useful for plugins or advanced usage).\n    if (Howler.usingWebAudio) {\n      Howler.masterGain = (typeof Howler.ctx.createGain === 'undefined') ? Howler.ctx.createGainNode() : Howler.ctx.createGain();\n      Howler.masterGain.gain.setValueAtTime(Howler._muted ? 0 : Howler._volume, Howler.ctx.currentTime);\n      Howler.masterGain.connect(Howler.ctx.destination);\n    }\n\n    // Re-run the setup on Howler.\n    Howler._setup();\n  };\n\n  // Add support for AMD (Asynchronous Module Definition) libraries such as require.js.\n  if (typeof define === 'function' && define.amd) {\n    define([], function() {\n      return {\n        Howler: Howler,\n        Howl: Howl\n      };\n    });\n  }\n\n  // Add support for CommonJS libraries such as browserify.\n  if (typeof exports !== 'undefined') {\n    exports.Howler = Howler;\n    exports.Howl = Howl;\n  }\n\n  // Add to global in Node.js (for testing, etc).\n  if (typeof global !== 'undefined') {\n    global.HowlerGlobal = HowlerGlobal;\n    global.Howler = Howler;\n    global.Howl = Howl;\n    global.Sound = Sound;\n  } else if (typeof window !== 'undefined') {  // Define globally in case AMD is not available or unused.\n    window.HowlerGlobal = HowlerGlobal;\n    window.Howler = Howler;\n    window.Howl = Howl;\n    window.Sound = Sound;\n  }\n})();\n\n\n/*!\n *  Spatial Plugin - Adds support for stereo and 3D audio where Web Audio is supported.\n *  \n *  howler.js v2.2.4\n *  howlerjs.com\n *\n *  (c) 2013-2020, James Simpson of GoldFire Studios\n *  goldfirestudios.com\n *\n *  MIT License\n */\n\n(function() {\n\n  'use strict';\n\n  // Setup default properties.\n  HowlerGlobal.prototype._pos = [0, 0, 0];\n  HowlerGlobal.prototype._orientation = [0, 0, -1, 0, 1, 0];\n\n  /** Global Methods **/\n  /***************************************************************************/\n\n  /**\n   * Helper method to update the stereo panning position of all current Howls.\n   * Future Howls will not use this value unless explicitly set.\n   * @param  {Number} pan A value of -1.0 is all the way left and 1.0 is all the way right.\n   * @return {Howler/Number}     Self or current stereo panning value.\n   */\n  HowlerGlobal.prototype.stereo = function(pan) {\n    var self = this;\n\n    // Stop right here if not using Web Audio.\n    if (!self.ctx || !self.ctx.listener) {\n      return self;\n    }\n\n    // Loop through all Howls and update their stereo panning.\n    for (var i=self._howls.length-1; i>=0; i--) {\n      self._howls[i].stereo(pan);\n    }\n\n    return self;\n  };\n\n  /**\n   * Get/set the position of the listener in 3D cartesian space. Sounds using\n   * 3D position will be relative to the listener's position.\n   * @param  {Number} x The x-position of the listener.\n   * @param  {Number} y The y-position of the listener.\n   * @param  {Number} z The z-position of the listener.\n   * @return {Howler/Array}   Self or current listener position.\n   */\n  HowlerGlobal.prototype.pos = function(x, y, z) {\n    var self = this;\n\n    // Stop right here if not using Web Audio.\n    if (!self.ctx || !self.ctx.listener) {\n      return self;\n    }\n\n    // Set the defaults for optional 'y' & 'z'.\n    y = (typeof y !== 'number') ? self._pos[1] : y;\n    z = (typeof z !== 'number') ? self._pos[2] : z;\n\n    if (typeof x === 'number') {\n      self._pos = [x, y, z];\n\n      if (typeof self.ctx.listener.positionX !== 'undefined') {\n        self.ctx.listener.positionX.setTargetAtTime(self._pos[0], Howler.ctx.currentTime, 0.1);\n        self.ctx.listener.positionY.setTargetAtTime(self._pos[1], Howler.ctx.currentTime, 0.1);\n        self.ctx.listener.positionZ.setTargetAtTime(self._pos[2], Howler.ctx.currentTime, 0.1);\n      } else {\n        self.ctx.listener.setPosition(self._pos[0], self._pos[1], self._pos[2]);\n      }\n    } else {\n      return self._pos;\n    }\n\n    return self;\n  };\n\n  /**\n   * Get/set the direction the listener is pointing in the 3D cartesian space.\n   * A front and up vector must be provided. The front is the direction the\n   * face of the listener is pointing, and up is the direction the top of the\n   * listener is pointing. Thus, these values are expected to be at right angles\n   * from each other.\n   * @param  {Number} x   The x-orientation of the listener.\n   * @param  {Number} y   The y-orientation of the listener.\n   * @param  {Number} z   The z-orientation of the listener.\n   * @param  {Number} xUp The x-orientation of the top of the listener.\n   * @param  {Number} yUp The y-orientation of the top of the listener.\n   * @param  {Number} zUp The z-orientation of the top of the listener.\n   * @return {Howler/Array}     Returns self or the current orientation vectors.\n   */\n  HowlerGlobal.prototype.orientation = function(x, y, z, xUp, yUp, zUp) {\n    var self = this;\n\n    // Stop right here if not using Web Audio.\n    if (!self.ctx || !self.ctx.listener) {\n      return self;\n    }\n\n    // Set the defaults for optional 'y' & 'z'.\n    var or = self._orientation;\n    y = (typeof y !== 'number') ? or[1] : y;\n    z = (typeof z !== 'number') ? or[2] : z;\n    xUp = (typeof xUp !== 'number') ? or[3] : xUp;\n    yUp = (typeof yUp !== 'number') ? or[4] : yUp;\n    zUp = (typeof zUp !== 'number') ? or[5] : zUp;\n\n    if (typeof x === 'number') {\n      self._orientation = [x, y, z, xUp, yUp, zUp];\n\n      if (typeof self.ctx.listener.forwardX !== 'undefined') {\n        self.ctx.listener.forwardX.setTargetAtTime(x, Howler.ctx.currentTime, 0.1);\n        self.ctx.listener.forwardY.setTargetAtTime(y, Howler.ctx.currentTime, 0.1);\n        self.ctx.listener.forwardZ.setTargetAtTime(z, Howler.ctx.currentTime, 0.1);\n        self.ctx.listener.upX.setTargetAtTime(xUp, Howler.ctx.currentTime, 0.1);\n        self.ctx.listener.upY.setTargetAtTime(yUp, Howler.ctx.currentTime, 0.1);\n        self.ctx.listener.upZ.setTargetAtTime(zUp, Howler.ctx.currentTime, 0.1);\n      } else {\n        self.ctx.listener.setOrientation(x, y, z, xUp, yUp, zUp);\n      }\n    } else {\n      return or;\n    }\n\n    return self;\n  };\n\n  /** Group Methods **/\n  /***************************************************************************/\n\n  /**\n   * Add new properties to the core init.\n   * @param  {Function} _super Core init method.\n   * @return {Howl}\n   */\n  Howl.prototype.init = (function(_super) {\n    return function(o) {\n      var self = this;\n\n      // Setup user-defined default properties.\n      self._orientation = o.orientation || [1, 0, 0];\n      self._stereo = o.stereo || null;\n      self._pos = o.pos || null;\n      self._pannerAttr = {\n        coneInnerAngle: typeof o.coneInnerAngle !== 'undefined' ? o.coneInnerAngle : 360,\n        coneOuterAngle: typeof o.coneOuterAngle !== 'undefined' ? o.coneOuterAngle : 360,\n        coneOuterGain: typeof o.coneOuterGain !== 'undefined' ? o.coneOuterGain : 0,\n        distanceModel: typeof o.distanceModel !== 'undefined' ? o.distanceModel : 'inverse',\n        maxDistance: typeof o.maxDistance !== 'undefined' ? o.maxDistance : 10000,\n        panningModel: typeof o.panningModel !== 'undefined' ? o.panningModel : 'HRTF',\n        refDistance: typeof o.refDistance !== 'undefined' ? o.refDistance : 1,\n        rolloffFactor: typeof o.rolloffFactor !== 'undefined' ? o.rolloffFactor : 1\n      };\n\n      // Setup event listeners.\n      self._onstereo = o.onstereo ? [{fn: o.onstereo}] : [];\n      self._onpos = o.onpos ? [{fn: o.onpos}] : [];\n      self._onorientation = o.onorientation ? [{fn: o.onorientation}] : [];\n\n      // Complete initilization with howler.js core's init function.\n      return _super.call(this, o);\n    };\n  })(Howl.prototype.init);\n\n  /**\n   * Get/set the stereo panning of the audio source for this sound or all in the group.\n   * @param  {Number} pan  A value of -1.0 is all the way left and 1.0 is all the way right.\n   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.\n   * @return {Howl/Number}    Returns self or the current stereo panning value.\n   */\n  Howl.prototype.stereo = function(pan, id) {\n    var self = this;\n\n    // Stop right here if not using Web Audio.\n    if (!self._webAudio) {\n      return self;\n    }\n\n    // If the sound hasn't loaded, add it to the load queue to change stereo pan when capable.\n    if (self._state !== 'loaded') {\n      self._queue.push({\n        event: 'stereo',\n        action: function() {\n          self.stereo(pan, id);\n        }\n      });\n\n      return self;\n    }\n\n    // Check for PannerStereoNode support and fallback to PannerNode if it doesn't exist.\n    var pannerType = (typeof Howler.ctx.createStereoPanner === 'undefined') ? 'spatial' : 'stereo';\n\n    // Setup the group's stereo panning if no ID is passed.\n    if (typeof id === 'undefined') {\n      // Return the group's stereo panning if no parameters are passed.\n      if (typeof pan === 'number') {\n        self._stereo = pan;\n        self._pos = [pan, 0, 0];\n      } else {\n        return self._stereo;\n      }\n    }\n\n    // Change the streo panning of one or all sounds in group.\n    var ids = self._getSoundIds(id);\n    for (var i=0; i<ids.length; i++) {\n      // Get the sound.\n      var sound = self._soundById(ids[i]);\n\n      if (sound) {\n        if (typeof pan === 'number') {\n          sound._stereo = pan;\n          sound._pos = [pan, 0, 0];\n\n          if (sound._node) {\n            // If we are falling back, make sure the panningModel is equalpower.\n            sound._pannerAttr.panningModel = 'equalpower';\n\n            // Check if there is a panner setup and create a new one if not.\n            if (!sound._panner || !sound._panner.pan) {\n              setupPanner(sound, pannerType);\n            }\n\n            if (pannerType === 'spatial') {\n              if (typeof sound._panner.positionX !== 'undefined') {\n                sound._panner.positionX.setValueAtTime(pan, Howler.ctx.currentTime);\n                sound._panner.positionY.setValueAtTime(0, Howler.ctx.currentTime);\n                sound._panner.positionZ.setValueAtTime(0, Howler.ctx.currentTime);\n              } else {\n                sound._panner.setPosition(pan, 0, 0);\n              }\n            } else {\n              sound._panner.pan.setValueAtTime(pan, Howler.ctx.currentTime);\n            }\n          }\n\n          self._emit('stereo', sound._id);\n        } else {\n          return sound._stereo;\n        }\n      }\n    }\n\n    return self;\n  };\n\n  /**\n   * Get/set the 3D spatial position of the audio source for this sound or group relative to the global listener.\n   * @param  {Number} x  The x-position of the audio source.\n   * @param  {Number} y  The y-position of the audio source.\n   * @param  {Number} z  The z-position of the audio source.\n   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.\n   * @return {Howl/Array}    Returns self or the current 3D spatial position: [x, y, z].\n   */\n  Howl.prototype.pos = function(x, y, z, id) {\n    var self = this;\n\n    // Stop right here if not using Web Audio.\n    if (!self._webAudio) {\n      return self;\n    }\n\n    // If the sound hasn't loaded, add it to the load queue to change position when capable.\n    if (self._state !== 'loaded') {\n      self._queue.push({\n        event: 'pos',\n        action: function() {\n          self.pos(x, y, z, id);\n        }\n      });\n\n      return self;\n    }\n\n    // Set the defaults for optional 'y' & 'z'.\n    y = (typeof y !== 'number') ? 0 : y;\n    z = (typeof z !== 'number') ? -0.5 : z;\n\n    // Setup the group's spatial position if no ID is passed.\n    if (typeof id === 'undefined') {\n      // Return the group's spatial position if no parameters are passed.\n      if (typeof x === 'number') {\n        self._pos = [x, y, z];\n      } else {\n        return self._pos;\n      }\n    }\n\n    // Change the spatial position of one or all sounds in group.\n    var ids = self._getSoundIds(id);\n    for (var i=0; i<ids.length; i++) {\n      // Get the sound.\n      var sound = self._soundById(ids[i]);\n\n      if (sound) {\n        if (typeof x === 'number') {\n          sound._pos = [x, y, z];\n\n          if (sound._node) {\n            // Check if there is a panner setup and create a new one if not.\n            if (!sound._panner || sound._panner.pan) {\n              setupPanner(sound, 'spatial');\n            }\n\n            if (typeof sound._panner.positionX !== 'undefined') {\n              sound._panner.positionX.setValueAtTime(x, Howler.ctx.currentTime);\n              sound._panner.positionY.setValueAtTime(y, Howler.ctx.currentTime);\n              sound._panner.positionZ.setValueAtTime(z, Howler.ctx.currentTime);\n            } else {\n              sound._panner.setPosition(x, y, z);\n            }\n          }\n\n          self._emit('pos', sound._id);\n        } else {\n          return sound._pos;\n        }\n      }\n    }\n\n    return self;\n  };\n\n  /**\n   * Get/set the direction the audio source is pointing in the 3D cartesian coordinate\n   * space. Depending on how direction the sound is, based on the `cone` attributes,\n   * a sound pointing away from the listener can be quiet or silent.\n   * @param  {Number} x  The x-orientation of the source.\n   * @param  {Number} y  The y-orientation of the source.\n   * @param  {Number} z  The z-orientation of the source.\n   * @param  {Number} id (optional) The sound ID. If none is passed, all in group will be updated.\n   * @return {Howl/Array}    Returns self or the current 3D spatial orientation: [x, y, z].\n   */\n  Howl.prototype.orientation = function(x, y, z, id) {\n    var self = this;\n\n    // Stop right here if not using Web Audio.\n    if (!self._webAudio) {\n      return self;\n    }\n\n    // If the sound hasn't loaded, add it to the load queue to change orientation when capable.\n    if (self._state !== 'loaded') {\n      self._queue.push({\n        event: 'orientation',\n        action: function() {\n          self.orientation(x, y, z, id);\n        }\n      });\n\n      return self;\n    }\n\n    // Set the defaults for optional 'y' & 'z'.\n    y = (typeof y !== 'number') ? self._orientation[1] : y;\n    z = (typeof z !== 'number') ? self._orientation[2] : z;\n\n    // Setup the group's spatial orientation if no ID is passed.\n    if (typeof id === 'undefined') {\n      // Return the group's spatial orientation if no parameters are passed.\n      if (typeof x === 'number') {\n        self._orientation = [x, y, z];\n      } else {\n        return self._orientation;\n      }\n    }\n\n    // Change the spatial orientation of one or all sounds in group.\n    var ids = self._getSoundIds(id);\n    for (var i=0; i<ids.length; i++) {\n      // Get the sound.\n      var sound = self._soundById(ids[i]);\n\n      if (sound) {\n        if (typeof x === 'number') {\n          sound._orientation = [x, y, z];\n\n          if (sound._node) {\n            // Check if there is a panner setup and create a new one if not.\n            if (!sound._panner) {\n              // Make sure we have a position to setup the node with.\n              if (!sound._pos) {\n                sound._pos = self._pos || [0, 0, -0.5];\n              }\n\n              setupPanner(sound, 'spatial');\n            }\n\n            if (typeof sound._panner.orientationX !== 'undefined') {\n              sound._panner.orientationX.setValueAtTime(x, Howler.ctx.currentTime);\n              sound._panner.orientationY.setValueAtTime(y, Howler.ctx.currentTime);\n              sound._panner.orientationZ.setValueAtTime(z, Howler.ctx.currentTime);\n            } else {\n              sound._panner.setOrientation(x, y, z);\n            }\n          }\n\n          self._emit('orientation', sound._id);\n        } else {\n          return sound._orientation;\n        }\n      }\n    }\n\n    return self;\n  };\n\n  /**\n   * Get/set the panner node's attributes for a sound or group of sounds.\n   * This method can optionall take 0, 1 or 2 arguments.\n   *   pannerAttr() -> Returns the group's values.\n   *   pannerAttr(id) -> Returns the sound id's values.\n   *   pannerAttr(o) -> Set's the values of all sounds in this Howl group.\n   *   pannerAttr(o, id) -> Set's the values of passed sound id.\n   *\n   *   Attributes:\n   *     coneInnerAngle - (360 by default) A parameter for directional audio sources, this is an angle, in degrees,\n   *                      inside of which there will be no volume reduction.\n   *     coneOuterAngle - (360 by default) A parameter for directional audio sources, this is an angle, in degrees,\n   *                      outside of which the volume will be reduced to a constant value of `coneOuterGain`.\n   *     coneOuterGain - (0 by default) A parameter for directional audio sources, this is the gain outside of the\n   *                     `coneOuterAngle`. It is a linear value in the range `[0, 1]`.\n   *     distanceModel - ('inverse' by default) Determines algorithm used to reduce volume as audio moves away from\n   *                     listener. Can be `linear`, `inverse` or `exponential.\n   *     maxDistance - (10000 by default) The maximum distance between source and listener, after which the volume\n   *                   will not be reduced any further.\n   *     refDistance - (1 by default) A reference distance for reducing volume as source moves further from the listener.\n   *                   This is simply a variable of the distance model and has a different effect depending on which model\n   *                   is used and the scale of your coordinates. Generally, volume will be equal to 1 at this distance.\n   *     rolloffFactor - (1 by default) How quickly the volume reduces as source moves from listener. This is simply a\n   *                     variable of the distance model and can be in the range of `[0, 1]` with `linear` and `[0, ∞]`\n   *                     with `inverse` and `exponential`.\n   *     panningModel - ('HRTF' by default) Determines which spatialization algorithm is used to position audio.\n   *                     Can be `HRTF` or `equalpower`.\n   *\n   * @return {Howl/Object} Returns self or current panner attributes.\n   */\n  Howl.prototype.pannerAttr = function() {\n    var self = this;\n    var args = arguments;\n    var o, id, sound;\n\n    // Stop right here if not using Web Audio.\n    if (!self._webAudio) {\n      return self;\n    }\n\n    // Determine the values based on arguments.\n    if (args.length === 0) {\n      // Return the group's panner attribute values.\n      return self._pannerAttr;\n    } else if (args.length === 1) {\n      if (typeof args[0] === 'object') {\n        o = args[0];\n\n        // Set the grou's panner attribute values.\n        if (typeof id === 'undefined') {\n          if (!o.pannerAttr) {\n            o.pannerAttr = {\n              coneInnerAngle: o.coneInnerAngle,\n              coneOuterAngle: o.coneOuterAngle,\n              coneOuterGain: o.coneOuterGain,\n              distanceModel: o.distanceModel,\n              maxDistance: o.maxDistance,\n              refDistance: o.refDistance,\n              rolloffFactor: o.rolloffFactor,\n              panningModel: o.panningModel\n            };\n          }\n\n          self._pannerAttr = {\n            coneInnerAngle: typeof o.pannerAttr.coneInnerAngle !== 'undefined' ? o.pannerAttr.coneInnerAngle : self._coneInnerAngle,\n            coneOuterAngle: typeof o.pannerAttr.coneOuterAngle !== 'undefined' ? o.pannerAttr.coneOuterAngle : self._coneOuterAngle,\n            coneOuterGain: typeof o.pannerAttr.coneOuterGain !== 'undefined' ? o.pannerAttr.coneOuterGain : self._coneOuterGain,\n            distanceModel: typeof o.pannerAttr.distanceModel !== 'undefined' ? o.pannerAttr.distanceModel : self._distanceModel,\n            maxDistance: typeof o.pannerAttr.maxDistance !== 'undefined' ? o.pannerAttr.maxDistance : self._maxDistance,\n            refDistance: typeof o.pannerAttr.refDistance !== 'undefined' ? o.pannerAttr.refDistance : self._refDistance,\n            rolloffFactor: typeof o.pannerAttr.rolloffFactor !== 'undefined' ? o.pannerAttr.rolloffFactor : self._rolloffFactor,\n            panningModel: typeof o.pannerAttr.panningModel !== 'undefined' ? o.pannerAttr.panningModel : self._panningModel\n          };\n        }\n      } else {\n        // Return this sound's panner attribute values.\n        sound = self._soundById(parseInt(args[0], 10));\n        return sound ? sound._pannerAttr : self._pannerAttr;\n      }\n    } else if (args.length === 2) {\n      o = args[0];\n      id = parseInt(args[1], 10);\n    }\n\n    // Update the values of the specified sounds.\n    var ids = self._getSoundIds(id);\n    for (var i=0; i<ids.length; i++) {\n      sound = self._soundById(ids[i]);\n\n      if (sound) {\n        // Merge the new values into the sound.\n        var pa = sound._pannerAttr;\n        pa = {\n          coneInnerAngle: typeof o.coneInnerAngle !== 'undefined' ? o.coneInnerAngle : pa.coneInnerAngle,\n          coneOuterAngle: typeof o.coneOuterAngle !== 'undefined' ? o.coneOuterAngle : pa.coneOuterAngle,\n          coneOuterGain: typeof o.coneOuterGain !== 'undefined' ? o.coneOuterGain : pa.coneOuterGain,\n          distanceModel: typeof o.distanceModel !== 'undefined' ? o.distanceModel : pa.distanceModel,\n          maxDistance: typeof o.maxDistance !== 'undefined' ? o.maxDistance : pa.maxDistance,\n          refDistance: typeof o.refDistance !== 'undefined' ? o.refDistance : pa.refDistance,\n          rolloffFactor: typeof o.rolloffFactor !== 'undefined' ? o.rolloffFactor : pa.rolloffFactor,\n          panningModel: typeof o.panningModel !== 'undefined' ? o.panningModel : pa.panningModel\n        };\n\n        // Create a new panner node if one doesn't already exist.\n        var panner = sound._panner;\n        if (!panner) {\n          // Make sure we have a position to setup the node with.\n          if (!sound._pos) {\n            sound._pos = self._pos || [0, 0, -0.5];\n          }\n\n          // Create a new panner node.\n          setupPanner(sound, 'spatial');\n          panner = sound._panner\n        }\n\n        // Update the panner values or create a new panner if none exists.\n        panner.coneInnerAngle = pa.coneInnerAngle;\n        panner.coneOuterAngle = pa.coneOuterAngle;\n        panner.coneOuterGain = pa.coneOuterGain;\n        panner.distanceModel = pa.distanceModel;\n        panner.maxDistance = pa.maxDistance;\n        panner.refDistance = pa.refDistance;\n        panner.rolloffFactor = pa.rolloffFactor;\n        panner.panningModel = pa.panningModel;\n      }\n    }\n\n    return self;\n  };\n\n  /** Single Sound Methods **/\n  /***************************************************************************/\n\n  /**\n   * Add new properties to the core Sound init.\n   * @param  {Function} _super Core Sound init method.\n   * @return {Sound}\n   */\n  Sound.prototype.init = (function(_super) {\n    return function() {\n      var self = this;\n      var parent = self._parent;\n\n      // Setup user-defined default properties.\n      self._orientation = parent._orientation;\n      self._stereo = parent._stereo;\n      self._pos = parent._pos;\n      self._pannerAttr = parent._pannerAttr;\n\n      // Complete initilization with howler.js core Sound's init function.\n      _super.call(this);\n\n      // If a stereo or position was specified, set it up.\n      if (self._stereo) {\n        parent.stereo(self._stereo);\n      } else if (self._pos) {\n        parent.pos(self._pos[0], self._pos[1], self._pos[2], self._id);\n      }\n    };\n  })(Sound.prototype.init);\n\n  /**\n   * Override the Sound.reset method to clean up properties from the spatial plugin.\n   * @param  {Function} _super Sound reset method.\n   * @return {Sound}\n   */\n  Sound.prototype.reset = (function(_super) {\n    return function() {\n      var self = this;\n      var parent = self._parent;\n\n      // Reset all spatial plugin properties on this sound.\n      self._orientation = parent._orientation;\n      self._stereo = parent._stereo;\n      self._pos = parent._pos;\n      self._pannerAttr = parent._pannerAttr;\n\n      // If a stereo or position was specified, set it up.\n      if (self._stereo) {\n        parent.stereo(self._stereo);\n      } else if (self._pos) {\n        parent.pos(self._pos[0], self._pos[1], self._pos[2], self._id);\n      } else if (self._panner) {\n        // Disconnect the panner.\n        self._panner.disconnect(0);\n        self._panner = undefined;\n        parent._refreshBuffer(self);\n      }\n\n      // Complete resetting of the sound.\n      return _super.call(this);\n    };\n  })(Sound.prototype.reset);\n\n  /** Helper Methods **/\n  /***************************************************************************/\n\n  /**\n   * Create a new panner node and save it on the sound.\n   * @param  {Sound} sound Specific sound to setup panning on.\n   * @param {String} type Type of panner to create: 'stereo' or 'spatial'.\n   */\n  var setupPanner = function(sound, type) {\n    type = type || 'spatial';\n\n    // Create the new panner node.\n    if (type === 'spatial') {\n      sound._panner = Howler.ctx.createPanner();\n      sound._panner.coneInnerAngle = sound._pannerAttr.coneInnerAngle;\n      sound._panner.coneOuterAngle = sound._pannerAttr.coneOuterAngle;\n      sound._panner.coneOuterGain = sound._pannerAttr.coneOuterGain;\n      sound._panner.distanceModel = sound._pannerAttr.distanceModel;\n      sound._panner.maxDistance = sound._pannerAttr.maxDistance;\n      sound._panner.refDistance = sound._pannerAttr.refDistance;\n      sound._panner.rolloffFactor = sound._pannerAttr.rolloffFactor;\n      sound._panner.panningModel = sound._pannerAttr.panningModel;\n\n      if (typeof sound._panner.positionX !== 'undefined') {\n        sound._panner.positionX.setValueAtTime(sound._pos[0], Howler.ctx.currentTime);\n        sound._panner.positionY.setValueAtTime(sound._pos[1], Howler.ctx.currentTime);\n        sound._panner.positionZ.setValueAtTime(sound._pos[2], Howler.ctx.currentTime);\n      } else {\n        sound._panner.setPosition(sound._pos[0], sound._pos[1], sound._pos[2]);\n      }\n\n      if (typeof sound._panner.orientationX !== 'undefined') {\n        sound._panner.orientationX.setValueAtTime(sound._orientation[0], Howler.ctx.currentTime);\n        sound._panner.orientationY.setValueAtTime(sound._orientation[1], Howler.ctx.currentTime);\n        sound._panner.orientationZ.setValueAtTime(sound._orientation[2], Howler.ctx.currentTime);\n      } else {\n        sound._panner.setOrientation(sound._orientation[0], sound._orientation[1], sound._orientation[2]);\n      }\n    } else {\n      sound._panner = Howler.ctx.createStereoPanner();\n      sound._panner.pan.setValueAtTime(sound._stereo, Howler.ctx.currentTime);\n    }\n\n    sound._panner.connect(sound._node);\n\n    // Update the connections.\n    if (!sound._paused) {\n      sound._parent.pause(sound._id, true).play(sound._id, true);\n    }\n  };\n})();\n"], "mappings": ";;;;;AAAA;AAAA;AAUA,KAAC,WAAW;AAEV;AASA,UAAIA,gBAAe,WAAW;AAC5B,aAAK,KAAK;AAAA,MACZ;AACA,MAAAA,cAAa,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,QAKvB,MAAM,WAAW;AACf,cAAI,OAAO,QAAQC;AAGnB,eAAK,WAAW;AAGhB,eAAK,kBAAkB,CAAC;AACxB,eAAK,gBAAgB;AAGrB,eAAK,UAAU,CAAC;AAChB,eAAK,SAAS,CAAC;AACf,eAAK,SAAS;AACd,eAAK,UAAU;AACf,eAAK,gBAAgB;AACrB,eAAK,aAAc,OAAO,WAAW,eAAe,OAAO,YAAa,OAAO,YAAY;AAG3F,eAAK,aAAa;AAClB,eAAK,UAAU;AACf,eAAK,gBAAgB;AACrB,eAAK,cAAc;AACnB,eAAK,MAAM;AAGX,eAAK,aAAa;AAGlB,eAAK,OAAO;AAEZ,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,QAAQ,SAAS,KAAK;AACpB,cAAI,OAAO,QAAQA;AACnB,gBAAM,WAAW,GAAG;AAGpB,cAAI,CAAC,KAAK,KAAK;AACb,8BAAkB;AAAA,UACpB;AAEA,cAAI,OAAO,QAAQ,eAAe,OAAO,KAAK,OAAO,GAAG;AACtD,iBAAK,UAAU;AAGf,gBAAI,KAAK,QAAQ;AACf,qBAAO;AAAA,YACT;AAGA,gBAAI,KAAK,eAAe;AACtB,mBAAK,WAAW,KAAK,eAAe,KAAKA,QAAO,IAAI,WAAW;AAAA,YACjE;AAGA,qBAAS,IAAE,GAAG,IAAE,KAAK,OAAO,QAAQ,KAAK;AACvC,kBAAI,CAAC,KAAK,OAAO,CAAC,EAAE,WAAW;AAE7B,oBAAI,MAAM,KAAK,OAAO,CAAC,EAAE,aAAa;AAGtC,yBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAC/B,sBAAI,QAAQ,KAAK,OAAO,CAAC,EAAE,WAAW,IAAI,CAAC,CAAC;AAE5C,sBAAI,SAAS,MAAM,OAAO;AACxB,0BAAM,MAAM,SAAS,MAAM,UAAU;AAAA,kBACvC;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAEA,mBAAO;AAAA,UACT;AAEA,iBAAO,KAAK;AAAA,QACd;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,MAAM,SAAS,OAAO;AACpB,cAAI,OAAO,QAAQA;AAGnB,cAAI,CAAC,KAAK,KAAK;AACb,8BAAkB;AAAA,UACpB;AAEA,eAAK,SAAS;AAGd,cAAI,KAAK,eAAe;AACtB,iBAAK,WAAW,KAAK,eAAe,QAAQ,IAAI,KAAK,SAASA,QAAO,IAAI,WAAW;AAAA,UACtF;AAGA,mBAAS,IAAE,GAAG,IAAE,KAAK,OAAO,QAAQ,KAAK;AACvC,gBAAI,CAAC,KAAK,OAAO,CAAC,EAAE,WAAW;AAE7B,kBAAI,MAAM,KAAK,OAAO,CAAC,EAAE,aAAa;AAGtC,uBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAC/B,oBAAI,QAAQ,KAAK,OAAO,CAAC,EAAE,WAAW,IAAI,CAAC,CAAC;AAE5C,oBAAI,SAAS,MAAM,OAAO;AACxB,wBAAM,MAAM,QAAS,QAAS,OAAO,MAAM;AAAA,gBAC7C;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA,QAKA,MAAM,WAAW;AACf,cAAI,OAAO,QAAQA;AAGnB,mBAAS,IAAE,GAAG,IAAE,KAAK,OAAO,QAAQ,KAAK;AACvC,iBAAK,OAAO,CAAC,EAAE,KAAK;AAAA,UACtB;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,QAAQ,WAAW;AACjB,cAAI,OAAO,QAAQA;AAEnB,mBAAS,IAAE,KAAK,OAAO,SAAO,GAAG,KAAG,GAAG,KAAK;AAC1C,iBAAK,OAAO,CAAC,EAAE,OAAO;AAAA,UACxB;AAGA,cAAI,KAAK,iBAAiB,KAAK,OAAO,OAAO,KAAK,IAAI,UAAU,aAAa;AAC3E,iBAAK,IAAI,MAAM;AACf,iBAAK,MAAM;AACX,8BAAkB;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,QAAQ,SAAS,KAAK;AACpB,kBAAQ,QAAQA,SAAQ,QAAQ,IAAI,QAAQ,OAAO,EAAE,CAAC;AAAA,QACxD;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,QAAQ,WAAW;AACjB,cAAI,OAAO,QAAQA;AAGnB,eAAK,QAAQ,KAAK,MAAM,KAAK,IAAI,SAAS,cAAc;AAGxD,eAAK,aAAa;AAGlB,cAAI,CAAC,KAAK,eAAe;AAEvB,gBAAI,OAAO,UAAU,aAAa;AAChC,kBAAI;AACF,oBAAI,OAAO,IAAI,MAAM;AAGrB,oBAAI,OAAO,KAAK,qBAAqB,aAAa;AAChD,uBAAK,gBAAgB;AAAA,gBACvB;AAAA,cACF,SAAQ,GAAG;AACT,qBAAK,UAAU;AAAA,cACjB;AAAA,YACF,OAAO;AACL,mBAAK,UAAU;AAAA,YACjB;AAAA,UACF;AAGA,cAAI;AACF,gBAAI,OAAO,IAAI,MAAM;AACrB,gBAAI,KAAK,OAAO;AACd,mBAAK,UAAU;AAAA,YACjB;AAAA,UACF,SAAS,GAAG;AAAA,UAAC;AAGb,cAAI,CAAC,KAAK,SAAS;AACjB,iBAAK,aAAa;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,cAAc,WAAW;AACvB,cAAI,OAAO,QAAQA;AACnB,cAAI,YAAY;AAGhB,cAAI;AACF,wBAAa,OAAO,UAAU,cAAe,IAAI,MAAM,IAAI;AAAA,UAC7D,SAAS,KAAK;AACZ,mBAAO;AAAA,UACT;AAEA,cAAI,CAAC,aAAa,OAAO,UAAU,gBAAgB,YAAY;AAC7D,mBAAO;AAAA,UACT;AAEA,cAAI,WAAW,UAAU,YAAY,aAAa,EAAE,QAAQ,QAAQ,EAAE;AAGtE,cAAI,KAAK,KAAK,aAAa,KAAK,WAAW,YAAY;AACvD,cAAI,aAAa,GAAG,MAAM,aAAa;AACvC,cAAI,aAAc,cAAc,SAAS,WAAW,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI;AAC5E,cAAI,cAAc,GAAG,QAAQ,QAAQ,MAAM,MAAM,GAAG,QAAQ,QAAQ,MAAM;AAC1E,cAAI,gBAAgB,GAAG,MAAM,iBAAiB;AAC9C,cAAI,cAAe,eAAe,iBAAiB,SAAS,cAAc,CAAC,GAAG,EAAE,IAAI;AAEpF,eAAK,UAAU;AAAA,YACb,KAAK,CAAC,EAAE,CAAC,eAAe,YAAY,UAAU,YAAY,YAAY,EAAE,QAAQ,QAAQ,EAAE;AAAA,YAC1F,MAAM,CAAC,CAAC;AAAA,YACR,MAAM,CAAC,CAAC,UAAU,YAAY,0BAA0B,EAAE,QAAQ,QAAQ,EAAE;AAAA,YAC5E,KAAK,CAAC,CAAC,UAAU,YAAY,4BAA4B,EAAE,QAAQ,QAAQ,EAAE;AAAA,YAC7E,KAAK,CAAC,CAAC,UAAU,YAAY,4BAA4B,EAAE,QAAQ,QAAQ,EAAE;AAAA,YAC7E,KAAK,CAAC,EAAE,UAAU,YAAY,uBAAuB,KAAK,UAAU,YAAY,WAAW,GAAG,QAAQ,QAAQ,EAAE;AAAA,YAChH,KAAK,CAAC,CAAC,UAAU,YAAY,YAAY,EAAE,QAAQ,QAAQ,EAAE;AAAA,YAC7D,KAAK,CAAC,CAAC,UAAU,YAAY,cAAc,EAAE,QAAQ,QAAQ,EAAE;AAAA,YAC/D,KAAK,CAAC,EAAE,UAAU,YAAY,cAAc,KAAK,UAAU,YAAY,YAAY,KAAK,UAAU,YAAY,YAAY,GAAG,QAAQ,QAAQ,EAAE;AAAA,YAC/I,KAAK,CAAC,EAAE,UAAU,YAAY,cAAc,KAAK,UAAU,YAAY,YAAY,KAAK,UAAU,YAAY,YAAY,GAAG,QAAQ,QAAQ,EAAE;AAAA,YAC/I,KAAK,CAAC,EAAE,UAAU,YAAY,cAAc,KAAK,UAAU,YAAY,YAAY,KAAK,UAAU,YAAY,YAAY,GAAG,QAAQ,QAAQ,EAAE;AAAA,YAC/I,MAAM,CAAC,EAAE,CAAC,eAAe,UAAU,YAAY,6BAA6B,EAAE,QAAQ,QAAQ,EAAE;AAAA,YAChG,MAAM,CAAC,EAAE,CAAC,eAAe,UAAU,YAAY,6BAA6B,EAAE,QAAQ,QAAQ,EAAE;AAAA,YAChG,OAAO,CAAC,CAAC,UAAU,YAAY,0BAA0B,EAAE,QAAQ,QAAQ,EAAE;AAAA,YAC7E,MAAM,CAAC,EAAE,UAAU,YAAY,eAAe,KAAK,UAAU,YAAY,aAAa,GAAG,QAAQ,QAAQ,EAAE;AAAA,UAC7G;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,cAAc,WAAW;AACvB,cAAI,OAAO,QAAQA;AAGnB,cAAI,KAAK,kBAAkB,CAAC,KAAK,KAAK;AACpC;AAAA,UACF;AAEA,eAAK,iBAAiB;AACtB,eAAK,aAAa;AAKlB,cAAI,CAAC,KAAK,mBAAmB,KAAK,IAAI,eAAe,OAAO;AAC1D,iBAAK,kBAAkB;AACvB,iBAAK,OAAO;AAAA,UACd;AAIA,eAAK,iBAAiB,KAAK,IAAI,aAAa,GAAG,GAAG,KAAK;AAKvD,cAAI,SAAS,SAAS,GAAG;AAOvB,mBAAO,KAAK,gBAAgB,SAAS,KAAK,eAAe;AACvD,kBAAI;AACF,oBAAI,YAAY,IAAI,MAAM;AAI1B,0BAAU,YAAY;AAGtB,qBAAK,mBAAmB,SAAS;AAAA,cACnC,SAASC,IAAG;AACV,qBAAK,UAAU;AACf;AAAA,cACF;AAAA,YACF;AAGA,qBAAS,IAAE,GAAG,IAAE,KAAK,OAAO,QAAQ,KAAK;AACvC,kBAAI,CAAC,KAAK,OAAO,CAAC,EAAE,WAAW;AAE7B,oBAAI,MAAM,KAAK,OAAO,CAAC,EAAE,aAAa;AAGtC,yBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAC/B,sBAAI,QAAQ,KAAK,OAAO,CAAC,EAAE,WAAW,IAAI,CAAC,CAAC;AAE5C,sBAAI,SAAS,MAAM,SAAS,CAAC,MAAM,MAAM,WAAW;AAClD,0BAAM,MAAM,YAAY;AACxB,0BAAM,MAAM,KAAK;AAAA,kBACnB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAGA,iBAAK,YAAY;AAGjB,gBAAI,SAAS,KAAK,IAAI,mBAAmB;AACzC,mBAAO,SAAS,KAAK;AACrB,mBAAO,QAAQ,KAAK,IAAI,WAAW;AAGnC,gBAAI,OAAO,OAAO,UAAU,aAAa;AACvC,qBAAO,OAAO,CAAC;AAAA,YACjB,OAAO;AACL,qBAAO,MAAM,CAAC;AAAA,YAChB;AAGA,gBAAI,OAAO,KAAK,IAAI,WAAW,YAAY;AACzC,mBAAK,IAAI,OAAO;AAAA,YAClB;AAGA,mBAAO,UAAU,WAAW;AAC1B,qBAAO,WAAW,CAAC;AAGnB,mBAAK,iBAAiB;AAGtB,uBAAS,oBAAoB,cAAc,QAAQ,IAAI;AACvD,uBAAS,oBAAoB,YAAY,QAAQ,IAAI;AACrD,uBAAS,oBAAoB,SAAS,QAAQ,IAAI;AAClD,uBAAS,oBAAoB,WAAW,QAAQ,IAAI;AAGpD,uBAASC,KAAE,GAAGA,KAAE,KAAK,OAAO,QAAQA,MAAK;AACvC,qBAAK,OAAOA,EAAC,EAAE,MAAM,QAAQ;AAAA,cAC/B;AAAA,YACF;AAAA,UACF;AAGA,mBAAS,iBAAiB,cAAc,QAAQ,IAAI;AACpD,mBAAS,iBAAiB,YAAY,QAAQ,IAAI;AAClD,mBAAS,iBAAiB,SAAS,QAAQ,IAAI;AAC/C,mBAAS,iBAAiB,WAAW,QAAQ,IAAI;AAEjD,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,mBAAmB,WAAW;AAC5B,cAAI,OAAO,QAAQF;AAGnB,cAAI,KAAK,gBAAgB,QAAQ;AAC/B,mBAAO,KAAK,gBAAgB,IAAI;AAAA,UAClC;AAGA,cAAI,WAAW,IAAI,MAAM,EAAE,KAAK;AAChC,cAAI,YAAY,OAAO,YAAY,gBAAgB,oBAAoB,WAAW,OAAO,SAAS,SAAS,aAAa;AACtH,qBAAS,MAAM,WAAW;AACxB,sBAAQ,KAAK,wEAAwE;AAAA,YACvF,CAAC;AAAA,UACH;AAEA,iBAAO,IAAI,MAAM;AAAA,QACnB;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,oBAAoB,SAAS,OAAO;AAClC,cAAI,OAAO,QAAQA;AAGnB,cAAI,MAAM,WAAW;AACnB,iBAAK,gBAAgB,KAAK,KAAK;AAAA,UACjC;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,cAAc,WAAW;AACvB,cAAI,OAAO;AAEX,cAAI,CAAC,KAAK,eAAe,CAAC,KAAK,OAAO,OAAO,KAAK,IAAI,YAAY,eAAe,CAACA,QAAO,eAAe;AACtG;AAAA,UACF;AAGA,mBAAS,IAAE,GAAG,IAAE,KAAK,OAAO,QAAQ,KAAK;AACvC,gBAAI,KAAK,OAAO,CAAC,EAAE,WAAW;AAC5B,uBAAS,IAAE,GAAG,IAAE,KAAK,OAAO,CAAC,EAAE,QAAQ,QAAQ,KAAK;AAClD,oBAAI,CAAC,KAAK,OAAO,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS;AACtC,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,cAAI,KAAK,eAAe;AACtB,yBAAa,KAAK,aAAa;AAAA,UACjC;AAGA,eAAK,gBAAgB,WAAW,WAAW;AACzC,gBAAI,CAAC,KAAK,aAAa;AACrB;AAAA,YACF;AAEA,iBAAK,gBAAgB;AACrB,iBAAK,QAAQ;AAGb,gBAAI,mBAAmB,WAAW;AAChC,mBAAK,QAAQ;AAEb,kBAAI,KAAK,qBAAqB;AAC5B,uBAAO,KAAK;AACZ,qBAAK,YAAY;AAAA,cACnB;AAAA,YACF;AAIA,iBAAK,IAAI,QAAQ,EAAE,KAAK,kBAAkB,gBAAgB;AAAA,UAC5D,GAAG,GAAK;AAER,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,aAAa,WAAW;AACtB,cAAI,OAAO;AAEX,cAAI,CAAC,KAAK,OAAO,OAAO,KAAK,IAAI,WAAW,eAAe,CAACA,QAAO,eAAe;AAChF;AAAA,UACF;AAEA,cAAI,KAAK,UAAU,aAAa,KAAK,IAAI,UAAU,iBAAiB,KAAK,eAAe;AACtF,yBAAa,KAAK,aAAa;AAC/B,iBAAK,gBAAgB;AAAA,UACvB,WAAW,KAAK,UAAU,eAAe,KAAK,UAAU,aAAa,KAAK,IAAI,UAAU,eAAe;AACrG,iBAAK,IAAI,OAAO,EAAE,KAAK,WAAW;AAChC,mBAAK,QAAQ;AAGb,uBAAS,IAAE,GAAG,IAAE,KAAK,OAAO,QAAQ,KAAK;AACvC,qBAAK,OAAO,CAAC,EAAE,MAAM,QAAQ;AAAA,cAC/B;AAAA,YACF,CAAC;AAED,gBAAI,KAAK,eAAe;AACtB,2BAAa,KAAK,aAAa;AAC/B,mBAAK,gBAAgB;AAAA,YACvB;AAAA,UACF,WAAW,KAAK,UAAU,cAAc;AACtC,iBAAK,sBAAsB;AAAA,UAC7B;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,UAAIA,UAAS,IAAID,cAAa;AAS9B,UAAII,QAAO,SAAS,GAAG;AACrB,YAAI,OAAO;AAGX,YAAI,CAAC,EAAE,OAAO,EAAE,IAAI,WAAW,GAAG;AAChC,kBAAQ,MAAM,4DAA4D;AAC1E;AAAA,QACF;AAEA,aAAK,KAAK,CAAC;AAAA,MACb;AACA,MAAAA,MAAK,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMf,MAAM,SAAS,GAAG;AAChB,cAAI,OAAO;AAGX,cAAI,CAACH,QAAO,KAAK;AACf,8BAAkB;AAAA,UACpB;AAGA,eAAK,YAAY,EAAE,YAAY;AAC/B,eAAK,UAAW,OAAO,EAAE,WAAW,WAAY,EAAE,SAAS,CAAC,EAAE,MAAM;AACpE,eAAK,SAAS,EAAE,SAAS;AACzB,eAAK,SAAS,EAAE,QAAQ;AACxB,eAAK,QAAQ,EAAE,QAAQ;AACvB,eAAK,QAAQ,EAAE,QAAQ;AACvB,eAAK,WAAY,OAAO,EAAE,YAAY,aAAa,EAAE,YAAY,aAAc,EAAE,UAAU;AAC3F,eAAK,QAAQ,EAAE,QAAQ;AACvB,eAAK,UAAU,EAAE,UAAU,CAAC;AAC5B,eAAK,OAAQ,OAAO,EAAE,QAAQ,WAAY,EAAE,MAAM,CAAC,EAAE,GAAG;AACxD,eAAK,UAAU,EAAE,WAAW,SAAY,EAAE,SAAS;AACnD,eAAK,OAAO;AAAA,YACV,QAAQ,EAAE,OAAO,EAAE,IAAI,SAAS,EAAE,IAAI,SAAS;AAAA,YAC/C,SAAS,EAAE,OAAO,EAAE,IAAI,UAAU,EAAE,IAAI,UAAU;AAAA,YAClD,iBAAiB,EAAE,OAAO,EAAE,IAAI,kBAAkB,EAAE,IAAI,kBAAkB;AAAA,UAC5E;AAGA,eAAK,YAAY;AACjB,eAAK,SAAS;AACd,eAAK,UAAU,CAAC;AAChB,eAAK,aAAa,CAAC;AACnB,eAAK,SAAS,CAAC;AACf,eAAK,YAAY;AAGjB,eAAK,SAAS,EAAE,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAK,CAAC,IAAI,CAAC;AAC3C,eAAK,UAAU,EAAE,SAAS,CAAC,EAAC,IAAI,EAAE,OAAM,CAAC,IAAI,CAAC;AAC9C,eAAK,UAAU,EAAE,SAAS,CAAC,EAAC,IAAI,EAAE,OAAM,CAAC,IAAI,CAAC;AAC9C,eAAK,eAAe,EAAE,cAAc,CAAC,EAAC,IAAI,EAAE,YAAW,CAAC,IAAI,CAAC;AAC7D,eAAK,eAAe,EAAE,cAAc,CAAC,EAAC,IAAI,EAAE,YAAW,CAAC,IAAI,CAAC;AAC7D,eAAK,WAAW,EAAE,UAAU,CAAC,EAAC,IAAI,EAAE,QAAO,CAAC,IAAI,CAAC;AACjD,eAAK,UAAU,EAAE,SAAS,CAAC,EAAC,IAAI,EAAE,OAAM,CAAC,IAAI,CAAC;AAC9C,eAAK,UAAU,EAAE,SAAS,CAAC,EAAC,IAAI,EAAE,OAAM,CAAC,IAAI,CAAC;AAC9C,eAAK,UAAU,EAAE,SAAS,CAAC,EAAC,IAAI,EAAE,OAAM,CAAC,IAAI,CAAC;AAC9C,eAAK,YAAY,EAAE,WAAW,CAAC,EAAC,IAAI,EAAE,SAAQ,CAAC,IAAI,CAAC;AACpD,eAAK,UAAU,EAAE,SAAS,CAAC,EAAC,IAAI,EAAE,OAAM,CAAC,IAAI,CAAC;AAC9C,eAAK,UAAU,EAAE,SAAS,CAAC,EAAC,IAAI,EAAE,OAAM,CAAC,IAAI,CAAC;AAC9C,eAAK,YAAY,EAAE,WAAW,CAAC,EAAC,IAAI,EAAE,SAAQ,CAAC,IAAI,CAAC;AACpD,eAAK,YAAY,CAAC;AAGlB,eAAK,YAAYA,QAAO,iBAAiB,CAAC,KAAK;AAG/C,cAAI,OAAOA,QAAO,QAAQ,eAAeA,QAAO,OAAOA,QAAO,YAAY;AACxE,YAAAA,QAAO,aAAa;AAAA,UACtB;AAGA,UAAAA,QAAO,OAAO,KAAK,IAAI;AAGvB,cAAI,KAAK,WAAW;AAClB,iBAAK,OAAO,KAAK;AAAA,cACf,OAAO;AAAA,cACP,QAAQ,WAAW;AACjB,qBAAK,KAAK;AAAA,cACZ;AAAA,YACF,CAAC;AAAA,UACH;AAGA,cAAI,KAAK,YAAY,KAAK,aAAa,QAAQ;AAC7C,iBAAK,KAAK;AAAA,UACZ;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,MAAM,WAAW;AACf,cAAI,OAAO;AACX,cAAI,MAAM;AAGV,cAAIA,QAAO,SAAS;AAClB,iBAAK,MAAM,aAAa,MAAM,mBAAmB;AACjD;AAAA,UACF;AAGA,cAAI,OAAO,KAAK,SAAS,UAAU;AACjC,iBAAK,OAAO,CAAC,KAAK,IAAI;AAAA,UACxB;AAGA,mBAAS,IAAE,GAAG,IAAE,KAAK,KAAK,QAAQ,KAAK;AACrC,gBAAI,KAAK;AAET,gBAAI,KAAK,WAAW,KAAK,QAAQ,CAAC,GAAG;AAEnC,oBAAM,KAAK,QAAQ,CAAC;AAAA,YACtB,OAAO;AAEL,oBAAM,KAAK,KAAK,CAAC;AACjB,kBAAI,OAAO,QAAQ,UAAU;AAC3B,qBAAK,MAAM,aAAa,MAAM,wDAAwD;AACtF;AAAA,cACF;AAGA,oBAAM,0BAA0B,KAAK,GAAG;AACxC,kBAAI,CAAC,KAAK;AACR,sBAAM,aAAa,KAAK,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,CAAC;AAAA,cAC9C;AAEA,kBAAI,KAAK;AACP,sBAAM,IAAI,CAAC,EAAE,YAAY;AAAA,cAC3B;AAAA,YACF;AAGA,gBAAI,CAAC,KAAK;AACR,sBAAQ,KAAK,4FAA4F;AAAA,YAC3G;AAGA,gBAAI,OAAOA,QAAO,OAAO,GAAG,GAAG;AAC7B,oBAAM,KAAK,KAAK,CAAC;AACjB;AAAA,YACF;AAAA,UACF;AAEA,cAAI,CAAC,KAAK;AACR,iBAAK,MAAM,aAAa,MAAM,8CAA8C;AAC5E;AAAA,UACF;AAEA,eAAK,OAAO;AACZ,eAAK,SAAS;AAId,cAAI,OAAO,SAAS,aAAa,YAAY,IAAI,MAAM,GAAG,CAAC,MAAM,SAAS;AACxE,iBAAK,SAAS;AACd,iBAAK,YAAY;AAAA,UACnB;AAGA,cAAII,OAAM,IAAI;AAGd,cAAI,KAAK,WAAW;AAClB,uBAAW,IAAI;AAAA,UACjB;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,MAAM,SAAS,QAAQ,UAAU;AAC/B,cAAI,OAAO;AACX,cAAI,KAAK;AAGT,cAAI,OAAO,WAAW,UAAU;AAC9B,iBAAK;AACL,qBAAS;AAAA,UACX,WAAW,OAAO,WAAW,YAAY,KAAK,WAAW,YAAY,CAAC,KAAK,QAAQ,MAAM,GAAG;AAE1F,mBAAO;AAAA,UACT,WAAW,OAAO,WAAW,aAAa;AAExC,qBAAS;AAIT,gBAAI,CAAC,KAAK,WAAW;AACnB,kBAAI,MAAM;AACV,uBAAS,IAAE,GAAG,IAAE,KAAK,QAAQ,QAAQ,KAAK;AACxC,oBAAI,KAAK,QAAQ,CAAC,EAAE,WAAW,CAAC,KAAK,QAAQ,CAAC,EAAE,QAAQ;AACtD;AACA,uBAAK,KAAK,QAAQ,CAAC,EAAE;AAAA,gBACvB;AAAA,cACF;AAEA,kBAAI,QAAQ,GAAG;AACb,yBAAS;AAAA,cACX,OAAO;AACL,qBAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAGA,cAAI,QAAQ,KAAK,KAAK,WAAW,EAAE,IAAI,KAAK,eAAe;AAG3D,cAAI,CAAC,OAAO;AACV,mBAAO;AAAA,UACT;AAGA,cAAI,MAAM,CAAC,QAAQ;AACjB,qBAAS,MAAM,WAAW;AAAA,UAC5B;AAKA,cAAI,KAAK,WAAW,UAAU;AAE5B,kBAAM,UAAU;AAGhB,kBAAM,SAAS;AAGf,gBAAI,UAAU,MAAM;AACpB,iBAAK,OAAO,KAAK;AAAA,cACf,OAAO;AAAA,cACP,QAAQ,WAAW;AACjB,qBAAK,KAAK,OAAO;AAAA,cACnB;AAAA,YACF,CAAC;AAED,mBAAO;AAAA,UACT;AAGA,cAAI,MAAM,CAAC,MAAM,SAAS;AAExB,gBAAI,CAAC,UAAU;AACb,mBAAK,WAAW,MAAM;AAAA,YACxB;AAEA,mBAAO,MAAM;AAAA,UACf;AAGA,cAAI,KAAK,WAAW;AAClB,YAAAJ,QAAO,YAAY;AAAA,UACrB;AAGA,cAAI,OAAO,KAAK,IAAI,GAAG,MAAM,QAAQ,IAAI,MAAM,QAAQ,KAAK,QAAQ,MAAM,EAAE,CAAC,IAAI,GAAI;AACrF,cAAI,WAAW,KAAK,IAAI,IAAK,KAAK,QAAQ,MAAM,EAAE,CAAC,IAAI,KAAK,QAAQ,MAAM,EAAE,CAAC,KAAK,MAAQ,IAAI;AAC9F,cAAI,UAAW,WAAW,MAAQ,KAAK,IAAI,MAAM,KAAK;AACtD,cAAI,QAAQ,KAAK,QAAQ,MAAM,EAAE,CAAC,IAAI;AACtC,cAAI,QAAQ,KAAK,QAAQ,MAAM,EAAE,CAAC,IAAI,KAAK,QAAQ,MAAM,EAAE,CAAC,KAAK;AACjE,gBAAM,UAAU;AAIhB,gBAAM,SAAS;AAGf,cAAI,YAAY,WAAW;AACzB,kBAAM,UAAU;AAChB,kBAAM,QAAQ;AACd,kBAAM,SAAS;AACf,kBAAM,QAAQ;AACd,kBAAM,QAAQ,CAAC,EAAE,MAAM,SAAS,KAAK,QAAQ,MAAM,EAAE,CAAC;AAAA,UACxD;AAGA,cAAI,QAAQ,MAAM;AAChB,iBAAK,OAAO,KAAK;AACjB;AAAA,UACF;AAGA,cAAI,OAAO,MAAM;AACjB,cAAI,KAAK,WAAW;AAElB,gBAAI,eAAe,WAAW;AAC5B,mBAAK,YAAY;AACjB,wBAAU;AACV,mBAAK,eAAe,KAAK;AAGzB,kBAAI,MAAO,MAAM,UAAU,KAAK,SAAU,IAAI,MAAM;AACpD,mBAAK,KAAK,eAAe,KAAKA,QAAO,IAAI,WAAW;AACpD,oBAAM,aAAaA,QAAO,IAAI;AAG9B,kBAAI,OAAO,KAAK,aAAa,UAAU,aAAa;AAClD,sBAAM,QAAQ,KAAK,aAAa,YAAY,GAAG,MAAM,KAAK,IAAI,KAAK,aAAa,YAAY,GAAG,MAAM,QAAQ;AAAA,cAC/G,OAAO;AACL,sBAAM,QAAQ,KAAK,aAAa,MAAM,GAAG,MAAM,KAAK,IAAI,KAAK,aAAa,MAAM,GAAG,MAAM,QAAQ;AAAA,cACnG;AAGA,kBAAI,YAAY,UAAU;AACxB,qBAAK,WAAW,MAAM,GAAG,IAAI,WAAW,KAAK,OAAO,KAAK,MAAM,KAAK,GAAG,OAAO;AAAA,cAChF;AAEA,kBAAI,CAAC,UAAU;AACb,2BAAW,WAAW;AACpB,uBAAK,MAAM,QAAQ,MAAM,GAAG;AAC5B,uBAAK,WAAW;AAAA,gBAClB,GAAG,CAAC;AAAA,cACN;AAAA,YACF;AAEA,gBAAIA,QAAO,UAAU,aAAaA,QAAO,IAAI,UAAU,eAAe;AACpE,2BAAa;AAAA,YACf,OAAO;AACL,mBAAK,YAAY;AAGjB,mBAAK,KAAK,UAAU,YAAY;AAGhC,mBAAK,YAAY,MAAM,GAAG;AAAA,YAC5B;AAAA,UACF,OAAO;AAEL,gBAAI,YAAY,WAAW;AACzB,mBAAK,cAAc;AACnB,mBAAK,QAAQ,MAAM,UAAU,KAAK,UAAUA,QAAO,UAAU,KAAK;AAClE,mBAAK,SAAS,MAAM,UAAUA,QAAO,OAAO;AAC5C,mBAAK,eAAe,MAAM;AAG1B,kBAAI;AACF,oBAAI,OAAO,KAAK,KAAK;AAGrB,oBAAI,QAAQ,OAAO,YAAY,gBAAgB,gBAAgB,WAAW,OAAO,KAAK,SAAS,aAAa;AAE1G,uBAAK,YAAY;AAGjB,4BAAU;AAGV,uBACG,KAAK,WAAW;AACf,yBAAK,YAAY;AACjB,yBAAK,YAAY;AACjB,wBAAI,CAAC,UAAU;AACb,2BAAK,MAAM,QAAQ,MAAM,GAAG;AAAA,oBAC9B,OAAO;AACL,2BAAK,WAAW;AAAA,oBAClB;AAAA,kBACF,CAAC,EACA,MAAM,WAAW;AAChB,yBAAK,YAAY;AACjB,yBAAK,MAAM,aAAa,MAAM,KAAK,6IAC+C;AAGlF,0BAAM,SAAS;AACf,0BAAM,UAAU;AAAA,kBAClB,CAAC;AAAA,gBACL,WAAW,CAAC,UAAU;AACpB,uBAAK,YAAY;AACjB,4BAAU;AACV,uBAAK,MAAM,QAAQ,MAAM,GAAG;AAAA,gBAC9B;AAGA,qBAAK,eAAe,MAAM;AAG1B,oBAAI,KAAK,QAAQ;AACf,uBAAK,MAAM,aAAa,MAAM,KAAK,6IAC+C;AAClF;AAAA,gBACF;AAGA,oBAAI,WAAW,eAAe,MAAM,OAAO;AACzC,uBAAK,WAAW,MAAM,GAAG,IAAI,WAAW,KAAK,OAAO,KAAK,MAAM,KAAK,GAAG,OAAO;AAAA,gBAChF,OAAO;AACL,uBAAK,WAAW,MAAM,GAAG,IAAI,WAAW;AAEtC,yBAAK,OAAO,KAAK;AAGjB,yBAAK,oBAAoB,SAAS,KAAK,WAAW,MAAM,GAAG,GAAG,KAAK;AAAA,kBACrE;AACA,uBAAK,iBAAiB,SAAS,KAAK,WAAW,MAAM,GAAG,GAAG,KAAK;AAAA,gBAClE;AAAA,cACF,SAAS,KAAK;AACZ,qBAAK,MAAM,aAAa,MAAM,KAAK,GAAG;AAAA,cACxC;AAAA,YACF;AAGA,gBAAI,KAAK,QAAQ,0FAA0F;AACzG,mBAAK,MAAM,KAAK;AAChB,mBAAK,KAAK;AAAA,YACZ;AAGA,gBAAI,qBAAsB,UAAU,OAAO,UAAY,CAAC,KAAK,cAAcA,QAAO,WAAW;AAC7F,gBAAI,KAAK,cAAc,KAAK,oBAAoB;AAC9C,wBAAU;AAAA,YACZ,OAAO;AACL,mBAAK,YAAY;AACjB,mBAAK,SAAS;AAEd,kBAAI,WAAW,WAAW;AACxB,qBAAK,SAAS;AAGd,0BAAU;AAGV,qBAAK,oBAAoBA,QAAO,eAAe,UAAU,KAAK;AAAA,cAChE;AACA,mBAAK,iBAAiBA,QAAO,eAAe,UAAU,KAAK;AAG3D,mBAAK,YAAY,MAAM,GAAG;AAAA,YAC5B;AAAA,UACF;AAEA,iBAAO,MAAM;AAAA,QACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,OAAO,SAAS,IAAI;AAClB,cAAI,OAAO;AAGX,cAAI,KAAK,WAAW,YAAY,KAAK,WAAW;AAC9C,iBAAK,OAAO,KAAK;AAAA,cACf,OAAO;AAAA,cACP,QAAQ,WAAW;AACjB,qBAAK,MAAM,EAAE;AAAA,cACf;AAAA,YACF,CAAC;AAED,mBAAO;AAAA,UACT;AAGA,cAAI,MAAM,KAAK,aAAa,EAAE;AAE9B,mBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAE/B,iBAAK,YAAY,IAAI,CAAC,CAAC;AAGvB,gBAAI,QAAQ,KAAK,WAAW,IAAI,CAAC,CAAC;AAElC,gBAAI,SAAS,CAAC,MAAM,SAAS;AAE3B,oBAAM,QAAQ,KAAK,KAAK,IAAI,CAAC,CAAC;AAC9B,oBAAM,YAAY;AAClB,oBAAM,UAAU;AAGhB,mBAAK,UAAU,IAAI,CAAC,CAAC;AAErB,kBAAI,MAAM,OAAO;AACf,oBAAI,KAAK,WAAW;AAElB,sBAAI,CAAC,MAAM,MAAM,cAAc;AAC7B;AAAA,kBACF;AAEA,sBAAI,OAAO,MAAM,MAAM,aAAa,SAAS,aAAa;AACxD,0BAAM,MAAM,aAAa,QAAQ,CAAC;AAAA,kBACpC,OAAO;AACL,0BAAM,MAAM,aAAa,KAAK,CAAC;AAAA,kBACjC;AAGA,uBAAK,aAAa,MAAM,KAAK;AAAA,gBAC/B,WAAW,CAAC,MAAM,MAAM,MAAM,QAAQ,KAAK,MAAM,MAAM,aAAa,UAAU;AAC5E,wBAAM,MAAM,MAAM;AAAA,gBACpB;AAAA,cACF;AAAA,YACF;AAGA,gBAAI,CAAC,UAAU,CAAC,GAAG;AACjB,mBAAK,MAAM,SAAS,QAAQ,MAAM,MAAM,IAAI;AAAA,YAC9C;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,MAAM,SAAS,IAAI,UAAU;AAC3B,cAAI,OAAO;AAGX,cAAI,KAAK,WAAW,YAAY,KAAK,WAAW;AAC9C,iBAAK,OAAO,KAAK;AAAA,cACf,OAAO;AAAA,cACP,QAAQ,WAAW;AACjB,qBAAK,KAAK,EAAE;AAAA,cACd;AAAA,YACF,CAAC;AAED,mBAAO;AAAA,UACT;AAGA,cAAI,MAAM,KAAK,aAAa,EAAE;AAE9B,mBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAE/B,iBAAK,YAAY,IAAI,CAAC,CAAC;AAGvB,gBAAI,QAAQ,KAAK,WAAW,IAAI,CAAC,CAAC;AAElC,gBAAI,OAAO;AAET,oBAAM,QAAQ,MAAM,UAAU;AAC9B,oBAAM,YAAY;AAClB,oBAAM,UAAU;AAChB,oBAAM,SAAS;AAGf,mBAAK,UAAU,IAAI,CAAC,CAAC;AAErB,kBAAI,MAAM,OAAO;AACf,oBAAI,KAAK,WAAW;AAElB,sBAAI,MAAM,MAAM,cAAc;AAC5B,wBAAI,OAAO,MAAM,MAAM,aAAa,SAAS,aAAa;AACxD,4BAAM,MAAM,aAAa,QAAQ,CAAC;AAAA,oBACpC,OAAO;AACL,4BAAM,MAAM,aAAa,KAAK,CAAC;AAAA,oBACjC;AAGA,yBAAK,aAAa,MAAM,KAAK;AAAA,kBAC/B;AAAA,gBACF,WAAW,CAAC,MAAM,MAAM,MAAM,QAAQ,KAAK,MAAM,MAAM,aAAa,UAAU;AAC5E,wBAAM,MAAM,cAAc,MAAM,UAAU;AAC1C,wBAAM,MAAM,MAAM;AAGlB,sBAAI,MAAM,MAAM,aAAa,UAAU;AACrC,yBAAK,YAAY,MAAM,KAAK;AAAA,kBAC9B;AAAA,gBACF;AAAA,cACF;AAEA,kBAAI,CAAC,UAAU;AACb,qBAAK,MAAM,QAAQ,MAAM,GAAG;AAAA,cAC9B;AAAA,YACF;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,MAAM,SAAS,OAAO,IAAI;AACxB,cAAI,OAAO;AAGX,cAAI,KAAK,WAAW,YAAW,KAAK,WAAW;AAC7C,iBAAK,OAAO,KAAK;AAAA,cACf,OAAO;AAAA,cACP,QAAQ,WAAW;AACjB,qBAAK,KAAK,OAAO,EAAE;AAAA,cACrB;AAAA,YACF,CAAC;AAED,mBAAO;AAAA,UACT;AAGA,cAAI,OAAO,OAAO,aAAa;AAC7B,gBAAI,OAAO,UAAU,WAAW;AAC9B,mBAAK,SAAS;AAAA,YAChB,OAAO;AACL,qBAAO,KAAK;AAAA,YACd;AAAA,UACF;AAGA,cAAI,MAAM,KAAK,aAAa,EAAE;AAE9B,mBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAE/B,gBAAI,QAAQ,KAAK,WAAW,IAAI,CAAC,CAAC;AAElC,gBAAI,OAAO;AACT,oBAAM,SAAS;AAGf,kBAAI,MAAM,WAAW;AACnB,qBAAK,UAAU,MAAM,GAAG;AAAA,cAC1B;AAEA,kBAAI,KAAK,aAAa,MAAM,OAAO;AACjC,sBAAM,MAAM,KAAK,eAAe,QAAQ,IAAI,MAAM,SAASA,QAAO,IAAI,WAAW;AAAA,cACnF,WAAW,MAAM,OAAO;AACtB,sBAAM,MAAM,QAAQA,QAAO,SAAS,OAAO;AAAA,cAC7C;AAEA,mBAAK,MAAM,QAAQ,MAAM,GAAG;AAAA,YAC9B;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAUA,QAAQ,WAAW;AACjB,cAAI,OAAO;AACX,cAAI,OAAO;AACX,cAAI,KAAK;AAGT,cAAI,KAAK,WAAW,GAAG;AAErB,mBAAO,KAAK;AAAA,UACd,WAAW,KAAK,WAAW,KAAK,KAAK,WAAW,KAAK,OAAO,KAAK,CAAC,MAAM,aAAa;AAEnF,gBAAI,MAAM,KAAK,aAAa;AAC5B,gBAAI,QAAQ,IAAI,QAAQ,KAAK,CAAC,CAAC;AAC/B,gBAAI,SAAS,GAAG;AACd,mBAAK,SAAS,KAAK,CAAC,GAAG,EAAE;AAAA,YAC3B,OAAO;AACL,oBAAM,WAAW,KAAK,CAAC,CAAC;AAAA,YAC1B;AAAA,UACF,WAAW,KAAK,UAAU,GAAG;AAC3B,kBAAM,WAAW,KAAK,CAAC,CAAC;AACxB,iBAAK,SAAS,KAAK,CAAC,GAAG,EAAE;AAAA,UAC3B;AAGA,cAAI;AACJ,cAAI,OAAO,QAAQ,eAAe,OAAO,KAAK,OAAO,GAAG;AAEtD,gBAAI,KAAK,WAAW,YAAW,KAAK,WAAW;AAC7C,mBAAK,OAAO,KAAK;AAAA,gBACf,OAAO;AAAA,gBACP,QAAQ,WAAW;AACjB,uBAAK,OAAO,MAAM,MAAM,IAAI;AAAA,gBAC9B;AAAA,cACF,CAAC;AAED,qBAAO;AAAA,YACT;AAGA,gBAAI,OAAO,OAAO,aAAa;AAC7B,mBAAK,UAAU;AAAA,YACjB;AAGA,iBAAK,KAAK,aAAa,EAAE;AACzB,qBAAS,IAAE,GAAG,IAAE,GAAG,QAAQ,KAAK;AAE9B,sBAAQ,KAAK,WAAW,GAAG,CAAC,CAAC;AAE7B,kBAAI,OAAO;AACT,sBAAM,UAAU;AAGhB,oBAAI,CAAC,KAAK,CAAC,GAAG;AACZ,uBAAK,UAAU,GAAG,CAAC,CAAC;AAAA,gBACtB;AAEA,oBAAI,KAAK,aAAa,MAAM,SAAS,CAAC,MAAM,QAAQ;AAClD,wBAAM,MAAM,KAAK,eAAe,KAAKA,QAAO,IAAI,WAAW;AAAA,gBAC7D,WAAW,MAAM,SAAS,CAAC,MAAM,QAAQ;AACvC,wBAAM,MAAM,SAAS,MAAMA,QAAO,OAAO;AAAA,gBAC3C;AAEA,qBAAK,MAAM,UAAU,MAAM,GAAG;AAAA,cAChC;AAAA,YACF;AAAA,UACF,OAAO;AACL,oBAAQ,KAAK,KAAK,WAAW,EAAE,IAAI,KAAK,QAAQ,CAAC;AACjD,mBAAO,QAAQ,MAAM,UAAU;AAAA,UACjC;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAUA,MAAM,SAAS,MAAM,IAAI,KAAK,IAAI;AAChC,cAAI,OAAO;AAGX,cAAI,KAAK,WAAW,YAAY,KAAK,WAAW;AAC9C,iBAAK,OAAO,KAAK;AAAA,cACf,OAAO;AAAA,cACP,QAAQ,WAAW;AACjB,qBAAK,KAAK,MAAM,IAAI,KAAK,EAAE;AAAA,cAC7B;AAAA,YACF,CAAC;AAED,mBAAO;AAAA,UACT;AAGA,iBAAO,KAAK,IAAI,KAAK,IAAI,GAAG,WAAW,IAAI,CAAC,GAAG,CAAC;AAChD,eAAK,KAAK,IAAI,KAAK,IAAI,GAAG,WAAW,EAAE,CAAC,GAAG,CAAC;AAC5C,gBAAM,WAAW,GAAG;AAGpB,eAAK,OAAO,MAAM,EAAE;AAGpB,cAAI,MAAM,KAAK,aAAa,EAAE;AAC9B,mBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAE/B,gBAAI,QAAQ,KAAK,WAAW,IAAI,CAAC,CAAC;AAGlC,gBAAI,OAAO;AAET,kBAAI,CAAC,IAAI;AACP,qBAAK,UAAU,IAAI,CAAC,CAAC;AAAA,cACvB;AAGA,kBAAI,KAAK,aAAa,CAAC,MAAM,QAAQ;AACnC,oBAAI,cAAcA,QAAO,IAAI;AAC7B,oBAAI,MAAM,cAAe,MAAM;AAC/B,sBAAM,UAAU;AAChB,sBAAM,MAAM,KAAK,eAAe,MAAM,WAAW;AACjD,sBAAM,MAAM,KAAK,wBAAwB,IAAI,GAAG;AAAA,cAClD;AAEA,mBAAK,mBAAmB,OAAO,MAAM,IAAI,KAAK,IAAI,CAAC,GAAG,OAAO,OAAO,WAAW;AAAA,YACjF;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAWA,oBAAoB,SAAS,OAAO,MAAM,IAAI,KAAK,IAAI,SAAS;AAC9D,cAAI,OAAO;AACX,cAAI,MAAM;AACV,cAAI,OAAO,KAAK;AAChB,cAAI,QAAQ,KAAK,IAAI,OAAO,IAAI;AAChC,cAAI,UAAU,KAAK,IAAI,GAAI,QAAQ,IAAK,MAAM,QAAQ,GAAG;AACzD,cAAI,WAAW,KAAK,IAAI;AAGxB,gBAAM,UAAU;AAGhB,gBAAM,YAAY,YAAY,WAAW;AAEvC,gBAAI,QAAQ,KAAK,IAAI,IAAI,YAAY;AACrC,uBAAW,KAAK,IAAI;AACpB,mBAAO,OAAO;AAGd,kBAAM,KAAK,MAAM,MAAM,GAAG,IAAI;AAG9B,gBAAI,OAAO,GAAG;AACZ,oBAAM,KAAK,IAAI,IAAI,GAAG;AAAA,YACxB,OAAO;AACL,oBAAM,KAAK,IAAI,IAAI,GAAG;AAAA,YACxB;AAGA,gBAAI,KAAK,WAAW;AAClB,oBAAM,UAAU;AAAA,YAClB,OAAO;AACL,mBAAK,OAAO,KAAK,MAAM,KAAK,IAAI;AAAA,YAClC;AAGA,gBAAI,SAAS;AACX,mBAAK,UAAU;AAAA,YACjB;AAGA,gBAAK,KAAK,QAAQ,OAAO,MAAQ,KAAK,QAAQ,OAAO,IAAK;AACxD,4BAAc,MAAM,SAAS;AAC7B,oBAAM,YAAY;AAClB,oBAAM,UAAU;AAChB,mBAAK,OAAO,IAAI,MAAM,GAAG;AACzB,mBAAK,MAAM,QAAQ,MAAM,GAAG;AAAA,YAC9B;AAAA,UACF,GAAG,OAAO;AAAA,QACZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,WAAW,SAAS,IAAI;AACtB,cAAI,OAAO;AACX,cAAI,QAAQ,KAAK,WAAW,EAAE;AAE9B,cAAI,SAAS,MAAM,WAAW;AAC5B,gBAAI,KAAK,WAAW;AAClB,oBAAM,MAAM,KAAK,sBAAsBA,QAAO,IAAI,WAAW;AAAA,YAC/D;AAEA,0BAAc,MAAM,SAAS;AAC7B,kBAAM,YAAY;AAClB,iBAAK,OAAO,MAAM,SAAS,EAAE;AAC7B,kBAAM,UAAU;AAChB,iBAAK,MAAM,QAAQ,EAAE;AAAA,UACvB;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAUA,MAAM,WAAW;AACf,cAAI,OAAO;AACX,cAAI,OAAO;AACX,cAAI,MAAM,IAAI;AAGd,cAAI,KAAK,WAAW,GAAG;AAErB,mBAAO,KAAK;AAAA,UACd,WAAW,KAAK,WAAW,GAAG;AAC5B,gBAAI,OAAO,KAAK,CAAC,MAAM,WAAW;AAChC,qBAAO,KAAK,CAAC;AACb,mBAAK,QAAQ;AAAA,YACf,OAAO;AAEL,sBAAQ,KAAK,WAAW,SAAS,KAAK,CAAC,GAAG,EAAE,CAAC;AAC7C,qBAAO,QAAQ,MAAM,QAAQ;AAAA,YAC/B;AAAA,UACF,WAAW,KAAK,WAAW,GAAG;AAC5B,mBAAO,KAAK,CAAC;AACb,iBAAK,SAAS,KAAK,CAAC,GAAG,EAAE;AAAA,UAC3B;AAGA,cAAI,MAAM,KAAK,aAAa,EAAE;AAC9B,mBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAC/B,oBAAQ,KAAK,WAAW,IAAI,CAAC,CAAC;AAE9B,gBAAI,OAAO;AACT,oBAAM,QAAQ;AACd,kBAAI,KAAK,aAAa,MAAM,SAAS,MAAM,MAAM,cAAc;AAC7D,sBAAM,MAAM,aAAa,OAAO;AAChC,oBAAI,MAAM;AACR,wBAAM,MAAM,aAAa,YAAY,MAAM,UAAU;AACrD,wBAAM,MAAM,aAAa,UAAU,MAAM;AAGzC,sBAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,GAAG;AACxB,yBAAK,MAAM,IAAI,CAAC,GAAG,IAAI;AACvB,yBAAK,KAAK,IAAI,CAAC,GAAG,IAAI;AAAA,kBACxB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAUA,MAAM,WAAW;AACf,cAAI,OAAO;AACX,cAAI,OAAO;AACX,cAAI,MAAM;AAGV,cAAI,KAAK,WAAW,GAAG;AAErB,iBAAK,KAAK,QAAQ,CAAC,EAAE;AAAA,UACvB,WAAW,KAAK,WAAW,GAAG;AAE5B,gBAAI,MAAM,KAAK,aAAa;AAC5B,gBAAI,QAAQ,IAAI,QAAQ,KAAK,CAAC,CAAC;AAC/B,gBAAI,SAAS,GAAG;AACd,mBAAK,SAAS,KAAK,CAAC,GAAG,EAAE;AAAA,YAC3B,OAAO;AACL,qBAAO,WAAW,KAAK,CAAC,CAAC;AAAA,YAC3B;AAAA,UACF,WAAW,KAAK,WAAW,GAAG;AAC5B,mBAAO,WAAW,KAAK,CAAC,CAAC;AACzB,iBAAK,SAAS,KAAK,CAAC,GAAG,EAAE;AAAA,UAC3B;AAGA,cAAI;AACJ,cAAI,OAAO,SAAS,UAAU;AAE5B,gBAAI,KAAK,WAAW,YAAY,KAAK,WAAW;AAC9C,mBAAK,OAAO,KAAK;AAAA,gBACf,OAAO;AAAA,gBACP,QAAQ,WAAW;AACjB,uBAAK,KAAK,MAAM,MAAM,IAAI;AAAA,gBAC5B;AAAA,cACF,CAAC;AAED,qBAAO;AAAA,YACT;AAGA,gBAAI,OAAO,OAAO,aAAa;AAC7B,mBAAK,QAAQ;AAAA,YACf;AAGA,iBAAK,KAAK,aAAa,EAAE;AACzB,qBAAS,IAAE,GAAG,IAAE,GAAG,QAAQ,KAAK;AAE9B,sBAAQ,KAAK,WAAW,GAAG,CAAC,CAAC;AAE7B,kBAAI,OAAO;AAGT,oBAAI,KAAK,QAAQ,GAAG,CAAC,CAAC,GAAG;AACvB,wBAAM,YAAY,KAAK,KAAK,GAAG,CAAC,CAAC;AACjC,wBAAM,aAAa,KAAK,YAAYA,QAAO,IAAI,cAAc,MAAM;AAAA,gBACrE;AACA,sBAAM,QAAQ;AAGd,oBAAI,KAAK,aAAa,MAAM,SAAS,MAAM,MAAM,cAAc;AAC7D,wBAAM,MAAM,aAAa,aAAa,eAAe,MAAMA,QAAO,IAAI,WAAW;AAAA,gBACnF,WAAW,MAAM,OAAO;AACtB,wBAAM,MAAM,eAAe;AAAA,gBAC7B;AAGA,oBAAI,OAAO,KAAK,KAAK,GAAG,CAAC,CAAC;AAC1B,oBAAI,YAAa,KAAK,QAAQ,MAAM,OAAO,EAAE,CAAC,IAAI,KAAK,QAAQ,MAAM,OAAO,EAAE,CAAC,KAAK,MAAQ;AAC5F,oBAAI,UAAW,WAAW,MAAQ,KAAK,IAAI,MAAM,KAAK;AAGtD,oBAAI,KAAK,WAAW,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,SAAS;AAC5C,uBAAK,YAAY,GAAG,CAAC,CAAC;AACtB,uBAAK,WAAW,GAAG,CAAC,CAAC,IAAI,WAAW,KAAK,OAAO,KAAK,MAAM,KAAK,GAAG,OAAO;AAAA,gBAC5E;AAEA,qBAAK,MAAM,QAAQ,MAAM,GAAG;AAAA,cAC9B;AAAA,YACF;AAAA,UACF,OAAO;AACL,oBAAQ,KAAK,WAAW,EAAE;AAC1B,mBAAO,QAAQ,MAAM,QAAQ,KAAK;AAAA,UACpC;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAUA,MAAM,WAAW;AACf,cAAI,OAAO;AACX,cAAI,OAAO;AACX,cAAI,MAAM;AAGV,cAAI,KAAK,WAAW,GAAG;AAErB,gBAAI,KAAK,QAAQ,QAAQ;AACvB,mBAAK,KAAK,QAAQ,CAAC,EAAE;AAAA,YACvB;AAAA,UACF,WAAW,KAAK,WAAW,GAAG;AAE5B,gBAAI,MAAM,KAAK,aAAa;AAC5B,gBAAI,QAAQ,IAAI,QAAQ,KAAK,CAAC,CAAC;AAC/B,gBAAI,SAAS,GAAG;AACd,mBAAK,SAAS,KAAK,CAAC,GAAG,EAAE;AAAA,YAC3B,WAAW,KAAK,QAAQ,QAAQ;AAC9B,mBAAK,KAAK,QAAQ,CAAC,EAAE;AACrB,qBAAO,WAAW,KAAK,CAAC,CAAC;AAAA,YAC3B;AAAA,UACF,WAAW,KAAK,WAAW,GAAG;AAC5B,mBAAO,WAAW,KAAK,CAAC,CAAC;AACzB,iBAAK,SAAS,KAAK,CAAC,GAAG,EAAE;AAAA,UAC3B;AAGA,cAAI,OAAO,OAAO,aAAa;AAC7B,mBAAO;AAAA,UACT;AAGA,cAAI,OAAO,SAAS,aAAa,KAAK,WAAW,YAAY,KAAK,YAAY;AAC5E,iBAAK,OAAO,KAAK;AAAA,cACf,OAAO;AAAA,cACP,QAAQ,WAAW;AACjB,qBAAK,KAAK,MAAM,MAAM,IAAI;AAAA,cAC5B;AAAA,YACF,CAAC;AAED,mBAAO;AAAA,UACT;AAGA,cAAI,QAAQ,KAAK,WAAW,EAAE;AAE9B,cAAI,OAAO;AACT,gBAAI,OAAO,SAAS,YAAY,QAAQ,GAAG;AAEzC,kBAAI,UAAU,KAAK,QAAQ,EAAE;AAC7B,kBAAI,SAAS;AACX,qBAAK,MAAM,IAAI,IAAI;AAAA,cACrB;AAGA,oBAAM,QAAQ;AACd,oBAAM,SAAS;AACf,mBAAK,YAAY,EAAE;AAGnB,kBAAI,CAAC,KAAK,aAAa,MAAM,SAAS,CAAC,MAAM,MAAM,MAAM,QAAQ,GAAG;AAClE,sBAAM,MAAM,cAAc;AAAA,cAC5B;AAGA,kBAAI,cAAc,WAAW;AAE3B,oBAAI,SAAS;AACX,uBAAK,KAAK,IAAI,IAAI;AAAA,gBACpB;AAEA,qBAAK,MAAM,QAAQ,EAAE;AAAA,cACvB;AAGA,kBAAI,WAAW,CAAC,KAAK,WAAW;AAC9B,oBAAI,WAAW,WAAW;AACxB,sBAAI,CAAC,KAAK,WAAW;AACnB,gCAAY;AAAA,kBACd,OAAO;AACL,+BAAW,UAAU,CAAC;AAAA,kBACxB;AAAA,gBACF;AACA,2BAAW,UAAU,CAAC;AAAA,cACxB,OAAO;AACL,4BAAY;AAAA,cACd;AAAA,YACF,OAAO;AACL,kBAAI,KAAK,WAAW;AAClB,oBAAI,WAAW,KAAK,QAAQ,EAAE,IAAIA,QAAO,IAAI,cAAc,MAAM,aAAa;AAC9E,oBAAI,WAAW,MAAM,YAAY,MAAM,YAAY,MAAM,QAAQ;AACjE,uBAAO,MAAM,SAAS,WAAW,WAAW,KAAK,IAAI,MAAM,KAAK;AAAA,cAClE,OAAO;AACL,uBAAO,MAAM,MAAM;AAAA,cACrB;AAAA,YACF;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,SAAS,SAAS,IAAI;AACpB,cAAI,OAAO;AAGX,cAAI,OAAO,OAAO,UAAU;AAC1B,gBAAI,QAAQ,KAAK,WAAW,EAAE;AAC9B,mBAAO,QAAQ,CAAC,MAAM,UAAU;AAAA,UAClC;AAGA,mBAAS,IAAE,GAAG,IAAE,KAAK,QAAQ,QAAQ,KAAK;AACxC,gBAAI,CAAC,KAAK,QAAQ,CAAC,EAAE,SAAS;AAC5B,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,UAAU,SAAS,IAAI;AACrB,cAAI,OAAO;AACX,cAAI,WAAW,KAAK;AAGpB,cAAI,QAAQ,KAAK,WAAW,EAAE;AAC9B,cAAI,OAAO;AACT,uBAAW,KAAK,QAAQ,MAAM,OAAO,EAAE,CAAC,IAAI;AAAA,UAC9C;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,OAAO,WAAW;AAChB,iBAAO,KAAK;AAAA,QACd;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,QAAQ,WAAW;AACjB,cAAI,OAAO;AAGX,cAAI,SAAS,KAAK;AAClB,mBAAS,IAAE,GAAG,IAAE,OAAO,QAAQ,KAAK;AAElC,gBAAI,CAAC,OAAO,CAAC,EAAE,SAAS;AACtB,mBAAK,KAAK,OAAO,CAAC,EAAE,GAAG;AAAA,YACzB;AAGA,gBAAI,CAAC,KAAK,WAAW;AAEnB,mBAAK,YAAY,OAAO,CAAC,EAAE,KAAK;AAGhC,qBAAO,CAAC,EAAE,MAAM,oBAAoB,SAAS,OAAO,CAAC,EAAE,UAAU,KAAK;AACtE,qBAAO,CAAC,EAAE,MAAM,oBAAoBA,QAAO,eAAe,OAAO,CAAC,EAAE,SAAS,KAAK;AAClF,qBAAO,CAAC,EAAE,MAAM,oBAAoB,SAAS,OAAO,CAAC,EAAE,QAAQ,KAAK;AAGpE,cAAAA,QAAO,mBAAmB,OAAO,CAAC,EAAE,KAAK;AAAA,YAC3C;AAGA,mBAAO,OAAO,CAAC,EAAE;AAGjB,iBAAK,YAAY,OAAO,CAAC,EAAE,GAAG;AAAA,UAChC;AAGA,cAAI,QAAQA,QAAO,OAAO,QAAQ,IAAI;AACtC,cAAI,SAAS,GAAG;AACd,YAAAA,QAAO,OAAO,OAAO,OAAO,CAAC;AAAA,UAC/B;AAGA,cAAI,WAAW;AACf,eAAK,IAAE,GAAG,IAAEA,QAAO,OAAO,QAAQ,KAAK;AACrC,gBAAIA,QAAO,OAAO,CAAC,EAAE,SAAS,KAAK,QAAQ,KAAK,KAAK,QAAQA,QAAO,OAAO,CAAC,EAAE,IAAI,KAAK,GAAG;AACxF,yBAAW;AACX;AAAA,YACF;AAAA,UACF;AAEA,cAAI,SAAS,UAAU;AACrB,mBAAO,MAAM,KAAK,IAAI;AAAA,UACxB;AAGA,UAAAA,QAAO,UAAU;AAGjB,eAAK,SAAS;AACd,eAAK,UAAU,CAAC;AAChB,iBAAO;AAEP,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAUA,IAAI,SAAS,OAAO,IAAI,IAAI,MAAM;AAChC,cAAI,OAAO;AACX,cAAI,SAAS,KAAK,QAAQ,KAAK;AAE/B,cAAI,OAAO,OAAO,YAAY;AAC5B,mBAAO,KAAK,OAAO,EAAC,IAAQ,IAAQ,KAAU,IAAI,EAAC,IAAQ,GAAM,CAAC;AAAA,UACpE;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASA,KAAK,SAAS,OAAO,IAAI,IAAI;AAC3B,cAAI,OAAO;AACX,cAAI,SAAS,KAAK,QAAQ,KAAK;AAC/B,cAAI,IAAI;AAGR,cAAI,OAAO,OAAO,UAAU;AAC1B,iBAAK;AACL,iBAAK;AAAA,UACP;AAEA,cAAI,MAAM,IAAI;AAEZ,iBAAK,IAAE,GAAG,IAAE,OAAO,QAAQ,KAAK;AAC9B,kBAAI,OAAQ,OAAO,OAAO,CAAC,EAAE;AAC7B,kBAAI,OAAO,OAAO,CAAC,EAAE,MAAM,QAAQ,CAAC,MAAM,MAAM;AAC9C,uBAAO,OAAO,GAAG,CAAC;AAClB;AAAA,cACF;AAAA,YACF;AAAA,UACF,WAAW,OAAO;AAEhB,iBAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,UACzB,OAAO;AAEL,gBAAI,OAAO,OAAO,KAAK,IAAI;AAC3B,iBAAK,IAAE,GAAG,IAAE,KAAK,QAAQ,KAAK;AAC5B,kBAAK,KAAK,CAAC,EAAE,QAAQ,KAAK,MAAM,KAAM,MAAM,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,GAAG;AAClE,qBAAK,KAAK,CAAC,CAAC,IAAI,CAAC;AAAA,cACnB;AAAA,YACF;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASA,MAAM,SAAS,OAAO,IAAI,IAAI;AAC5B,cAAI,OAAO;AAGX,eAAK,GAAG,OAAO,IAAI,IAAI,CAAC;AAExB,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASA,OAAO,SAAS,OAAO,IAAI,KAAK;AAC9B,cAAI,OAAO;AACX,cAAI,SAAS,KAAK,QAAQ,KAAK;AAG/B,mBAAS,IAAE,OAAO,SAAO,GAAG,KAAG,GAAG,KAAK;AAErC,gBAAI,CAAC,OAAO,CAAC,EAAE,MAAM,OAAO,CAAC,EAAE,OAAO,MAAM,UAAU,QAAQ;AAC5D,0BAAW,SAAS,IAAI;AACtB,mBAAG,KAAK,MAAM,IAAI,GAAG;AAAA,cACvB,GAAE,KAAK,MAAM,OAAO,CAAC,EAAE,EAAE,GAAG,CAAC;AAG7B,kBAAI,OAAO,CAAC,EAAE,MAAM;AAClB,qBAAK,IAAI,OAAO,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,EAAE;AAAA,cAC5C;AAAA,YACF;AAAA,UACF;AAGA,eAAK,WAAW,KAAK;AAErB,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,YAAY,SAAS,OAAO;AAC1B,cAAI,OAAO;AAEX,cAAI,KAAK,OAAO,SAAS,GAAG;AAC1B,gBAAI,OAAO,KAAK,OAAO,CAAC;AAGxB,gBAAI,KAAK,UAAU,OAAO;AACxB,mBAAK,OAAO,MAAM;AAClB,mBAAK,WAAW;AAAA,YAClB;AAGA,gBAAI,CAAC,OAAO;AACV,mBAAK,OAAO;AAAA,YACd;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,QAAQ,SAAS,OAAO;AACtB,cAAI,OAAO;AACX,cAAI,SAAS,MAAM;AAKnB,cAAI,CAAC,KAAK,aAAa,MAAM,SAAS,CAAC,MAAM,MAAM,UAAU,CAAC,MAAM,MAAM,SAAS,MAAM,MAAM,cAAc,MAAM,OAAO;AACxH,uBAAW,KAAK,OAAO,KAAK,MAAM,KAAK,GAAG,GAAG;AAC7C,mBAAO;AAAA,UACT;AAGA,cAAI,OAAO,CAAC,EAAE,MAAM,SAAS,KAAK,QAAQ,MAAM,EAAE,CAAC;AAGnD,eAAK,MAAM,OAAO,MAAM,GAAG;AAG3B,cAAI,CAAC,KAAK,aAAa,MAAM;AAC3B,iBAAK,KAAK,MAAM,KAAK,IAAI,EAAE,KAAK,MAAM,GAAG;AAAA,UAC3C;AAGA,cAAI,KAAK,aAAa,MAAM;AAC1B,iBAAK,MAAM,QAAQ,MAAM,GAAG;AAC5B,kBAAM,QAAQ,MAAM,UAAU;AAC9B,kBAAM,YAAY;AAClB,kBAAM,aAAaA,QAAO,IAAI;AAE9B,gBAAI,WAAY,MAAM,QAAQ,MAAM,UAAU,MAAQ,KAAK,IAAI,MAAM,KAAK;AAC1E,iBAAK,WAAW,MAAM,GAAG,IAAI,WAAW,KAAK,OAAO,KAAK,MAAM,KAAK,GAAG,OAAO;AAAA,UAChF;AAGA,cAAI,KAAK,aAAa,CAAC,MAAM;AAC3B,kBAAM,UAAU;AAChB,kBAAM,SAAS;AACf,kBAAM,QAAQ,MAAM,UAAU;AAC9B,kBAAM,YAAY;AAClB,iBAAK,YAAY,MAAM,GAAG;AAG1B,iBAAK,aAAa,MAAM,KAAK;AAG7B,YAAAA,QAAO,aAAa;AAAA,UACtB;AAGA,cAAI,CAAC,KAAK,aAAa,CAAC,MAAM;AAC5B,iBAAK,KAAK,MAAM,KAAK,IAAI;AAAA,UAC3B;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,aAAa,SAAS,IAAI;AACxB,cAAI,OAAO;AAEX,cAAI,KAAK,WAAW,EAAE,GAAG;AAEvB,gBAAI,OAAO,KAAK,WAAW,EAAE,MAAM,YAAY;AAC7C,2BAAa,KAAK,WAAW,EAAE,CAAC;AAAA,YAClC,OAAO;AACL,kBAAI,QAAQ,KAAK,WAAW,EAAE;AAC9B,kBAAI,SAAS,MAAM,OAAO;AACxB,sBAAM,MAAM,oBAAoB,SAAS,KAAK,WAAW,EAAE,GAAG,KAAK;AAAA,cACrE;AAAA,YACF;AAEA,mBAAO,KAAK,WAAW,EAAE;AAAA,UAC3B;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,YAAY,SAAS,IAAI;AACvB,cAAI,OAAO;AAGX,mBAAS,IAAE,GAAG,IAAE,KAAK,QAAQ,QAAQ,KAAK;AACxC,gBAAI,OAAO,KAAK,QAAQ,CAAC,EAAE,KAAK;AAC9B,qBAAO,KAAK,QAAQ,CAAC;AAAA,YACvB;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,gBAAgB,WAAW;AACzB,cAAI,OAAO;AAEX,eAAK,OAAO;AAGZ,mBAAS,IAAE,GAAG,IAAE,KAAK,QAAQ,QAAQ,KAAK;AACxC,gBAAI,KAAK,QAAQ,CAAC,EAAE,QAAQ;AAC1B,qBAAO,KAAK,QAAQ,CAAC,EAAE,MAAM;AAAA,YAC/B;AAAA,UACF;AAGA,iBAAO,IAAII,OAAM,IAAI;AAAA,QACvB;AAAA;AAAA;AAAA;AAAA,QAKA,QAAQ,WAAW;AACjB,cAAI,OAAO;AACX,cAAI,QAAQ,KAAK;AACjB,cAAI,MAAM;AACV,cAAI,IAAI;AAGR,cAAI,KAAK,QAAQ,SAAS,OAAO;AAC/B;AAAA,UACF;AAGA,eAAK,IAAE,GAAG,IAAE,KAAK,QAAQ,QAAQ,KAAK;AACpC,gBAAI,KAAK,QAAQ,CAAC,EAAE,QAAQ;AAC1B;AAAA,YACF;AAAA,UACF;AAGA,eAAK,IAAE,KAAK,QAAQ,SAAS,GAAG,KAAG,GAAG,KAAK;AACzC,gBAAI,OAAO,OAAO;AAChB;AAAA,YACF;AAEA,gBAAI,KAAK,QAAQ,CAAC,EAAE,QAAQ;AAE1B,kBAAI,KAAK,aAAa,KAAK,QAAQ,CAAC,EAAE,OAAO;AAC3C,qBAAK,QAAQ,CAAC,EAAE,MAAM,WAAW,CAAC;AAAA,cACpC;AAGA,mBAAK,QAAQ,OAAO,GAAG,CAAC;AACxB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,cAAc,SAAS,IAAI;AACzB,cAAI,OAAO;AAEX,cAAI,OAAO,OAAO,aAAa;AAC7B,gBAAI,MAAM,CAAC;AACX,qBAAS,IAAE,GAAG,IAAE,KAAK,QAAQ,QAAQ,KAAK;AACxC,kBAAI,KAAK,KAAK,QAAQ,CAAC,EAAE,GAAG;AAAA,YAC9B;AAEA,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,CAAC,EAAE;AAAA,UACZ;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,gBAAgB,SAAS,OAAO;AAC9B,cAAI,OAAO;AAGX,gBAAM,MAAM,eAAeJ,QAAO,IAAI,mBAAmB;AACzD,gBAAM,MAAM,aAAa,SAAS,MAAM,KAAK,IAAI;AAGjD,cAAI,MAAM,SAAS;AACjB,kBAAM,MAAM,aAAa,QAAQ,MAAM,OAAO;AAAA,UAChD,OAAO;AACL,kBAAM,MAAM,aAAa,QAAQ,MAAM,KAAK;AAAA,UAC9C;AAGA,gBAAM,MAAM,aAAa,OAAO,MAAM;AACtC,cAAI,MAAM,OAAO;AACf,kBAAM,MAAM,aAAa,YAAY,MAAM,UAAU;AACrD,kBAAM,MAAM,aAAa,UAAU,MAAM,SAAS;AAAA,UACpD;AACA,gBAAM,MAAM,aAAa,aAAa,eAAe,MAAM,OAAOA,QAAO,IAAI,WAAW;AAExF,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,cAAc,SAAS,MAAM;AAC3B,cAAI,OAAO;AACX,cAAI,QAAQA,QAAO,cAAcA,QAAO,WAAW,OAAO,QAAQ,OAAO,KAAK;AAE9E,cAAI,CAAC,KAAK,cAAc;AACtB,mBAAO;AAAA,UACT;AAEA,cAAIA,QAAO,kBAAkB,KAAK,cAAc;AAC9C,iBAAK,aAAa,UAAU;AAC5B,iBAAK,aAAa,WAAW,CAAC;AAC9B,gBAAI,OAAO;AACT,kBAAI;AAAE,qBAAK,aAAa,SAASA,QAAO;AAAA,cAAgB,SAAQ,GAAG;AAAA,cAAC;AAAA,YACtE;AAAA,UACF;AACA,eAAK,eAAe;AAEpB,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,aAAa,SAAS,MAAM;AAC1B,cAAI,UAAU,kBAAkB,KAAKA,QAAO,cAAcA,QAAO,WAAW,SAAS;AACrF,cAAI,CAAC,SAAS;AACZ,iBAAK,MAAM;AAAA,UACb;AAAA,QACF;AAAA,MACF;AASA,UAAII,SAAQ,SAAS,MAAM;AACzB,aAAK,UAAU;AACf,aAAK,KAAK;AAAA,MACZ;AACA,MAAAA,OAAM,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,QAKhB,MAAM,WAAW;AACf,cAAI,OAAO;AACX,cAAI,SAAS,KAAK;AAGlB,eAAK,SAAS,OAAO;AACrB,eAAK,QAAQ,OAAO;AACpB,eAAK,UAAU,OAAO;AACtB,eAAK,QAAQ,OAAO;AACpB,eAAK,QAAQ;AACb,eAAK,UAAU;AACf,eAAK,SAAS;AACd,eAAK,UAAU;AAGf,eAAK,MAAM,EAAEJ,QAAO;AAGpB,iBAAO,QAAQ,KAAK,IAAI;AAGxB,eAAK,OAAO;AAEZ,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,QAAQ,WAAW;AACjB,cAAI,OAAO;AACX,cAAI,SAAS,KAAK;AAClB,cAAI,SAAUA,QAAO,UAAU,KAAK,UAAU,KAAK,QAAQ,SAAU,IAAI,KAAK;AAE9E,cAAI,OAAO,WAAW;AAEpB,iBAAK,QAAS,OAAOA,QAAO,IAAI,eAAe,cAAeA,QAAO,IAAI,eAAe,IAAIA,QAAO,IAAI,WAAW;AAClH,iBAAK,MAAM,KAAK,eAAe,QAAQA,QAAO,IAAI,WAAW;AAC7D,iBAAK,MAAM,SAAS;AACpB,iBAAK,MAAM,QAAQA,QAAO,UAAU;AAAA,UACtC,WAAW,CAACA,QAAO,SAAS;AAE1B,iBAAK,QAAQA,QAAO,kBAAkB;AAGtC,iBAAK,WAAW,KAAK,eAAe,KAAK,IAAI;AAC7C,iBAAK,MAAM,iBAAiB,SAAS,KAAK,UAAU,KAAK;AAGzD,iBAAK,UAAU,KAAK,cAAc,KAAK,IAAI;AAC3C,iBAAK,MAAM,iBAAiBA,QAAO,eAAe,KAAK,SAAS,KAAK;AAIrE,iBAAK,SAAS,KAAK,aAAa,KAAK,IAAI;AACzC,iBAAK,MAAM,iBAAiB,SAAS,KAAK,QAAQ,KAAK;AAGvD,iBAAK,MAAM,MAAM,OAAO;AACxB,iBAAK,MAAM,UAAU,OAAO,aAAa,OAAO,SAAS,OAAO;AAChE,iBAAK,MAAM,SAAS,SAASA,QAAO,OAAO;AAG3C,iBAAK,MAAM,KAAK;AAAA,UAClB;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,OAAO,WAAW;AAChB,cAAI,OAAO;AACX,cAAI,SAAS,KAAK;AAGlB,eAAK,SAAS,OAAO;AACrB,eAAK,QAAQ,OAAO;AACpB,eAAK,UAAU,OAAO;AACtB,eAAK,QAAQ,OAAO;AACpB,eAAK,QAAQ;AACb,eAAK,YAAY;AACjB,eAAK,UAAU;AACf,eAAK,SAAS;AACd,eAAK,UAAU;AAGf,eAAK,MAAM,EAAEA,QAAO;AAEpB,iBAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA,QAKA,gBAAgB,WAAW;AACzB,cAAI,OAAO;AAGX,eAAK,QAAQ,MAAM,aAAa,KAAK,KAAK,KAAK,MAAM,QAAQ,KAAK,MAAM,MAAM,OAAO,CAAC;AAGtF,eAAK,MAAM,oBAAoB,SAAS,KAAK,UAAU,KAAK;AAAA,QAC9D;AAAA;AAAA;AAAA;AAAA,QAKA,eAAe,WAAW;AACxB,cAAI,OAAO;AACX,cAAI,SAAS,KAAK;AAGlB,iBAAO,YAAY,KAAK,KAAK,KAAK,MAAM,WAAW,EAAE,IAAI;AAGzD,cAAI,OAAO,KAAK,OAAO,OAAO,EAAE,WAAW,GAAG;AAC5C,mBAAO,UAAU,EAAC,WAAW,CAAC,GAAG,OAAO,YAAY,GAAI,EAAC;AAAA,UAC3D;AAEA,cAAI,OAAO,WAAW,UAAU;AAC9B,mBAAO,SAAS;AAChB,mBAAO,MAAM,MAAM;AACnB,mBAAO,WAAW;AAAA,UACpB;AAGA,eAAK,MAAM,oBAAoBA,QAAO,eAAe,KAAK,SAAS,KAAK;AAAA,QAC1E;AAAA;AAAA;AAAA;AAAA,QAKA,cAAc,WAAW;AACvB,cAAI,OAAO;AACX,cAAI,SAAS,KAAK;AAGlB,cAAI,OAAO,cAAc,UAAU;AAGjC,mBAAO,YAAY,KAAK,KAAK,KAAK,MAAM,WAAW,EAAE,IAAI;AAGzD,gBAAI,OAAO,QAAQ,UAAU,CAAC,MAAM,UAAU;AAC5C,qBAAO,QAAQ,UAAU,CAAC,IAAI,OAAO,YAAY;AAAA,YACnD;AAGA,mBAAO,OAAO,IAAI;AAAA,UACpB;AAGA,eAAK,MAAM,oBAAoB,SAAS,KAAK,QAAQ,KAAK;AAAA,QAC5D;AAAA,MACF;AAKA,UAAI,QAAQ,CAAC;AAMb,UAAI,aAAa,SAAS,MAAM;AAC9B,YAAI,MAAM,KAAK;AAGf,YAAI,MAAM,GAAG,GAAG;AAEd,eAAK,YAAY,MAAM,GAAG,EAAE;AAG5B,oBAAU,IAAI;AAEd;AAAA,QACF;AAEA,YAAI,sBAAsB,KAAK,GAAG,GAAG;AAEnC,cAAI,OAAO,KAAK,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC;AACjC,cAAI,WAAW,IAAI,WAAW,KAAK,MAAM;AACzC,mBAAS,IAAE,GAAG,IAAE,KAAK,QAAQ,EAAE,GAAG;AAChC,qBAAS,CAAC,IAAI,KAAK,WAAW,CAAC;AAAA,UACjC;AAEA,0BAAgB,SAAS,QAAQ,IAAI;AAAA,QACvC,OAAO;AAEL,cAAI,MAAM,IAAI,eAAe;AAC7B,cAAI,KAAK,KAAK,KAAK,QAAQ,KAAK,IAAI;AACpC,cAAI,kBAAkB,KAAK,KAAK;AAChC,cAAI,eAAe;AAGnB,cAAI,KAAK,KAAK,SAAS;AACrB,mBAAO,KAAK,KAAK,KAAK,OAAO,EAAE,QAAQ,SAAS,KAAK;AACnD,kBAAI,iBAAiB,KAAK,KAAK,KAAK,QAAQ,GAAG,CAAC;AAAA,YAClD,CAAC;AAAA,UACH;AAEA,cAAI,SAAS,WAAW;AAEtB,gBAAI,QAAQ,IAAI,SAAS,IAAI,CAAC;AAC9B,gBAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;AAChD,mBAAK,MAAM,aAAa,MAAM,4CAA4C,IAAI,SAAS,GAAG;AAC1F;AAAA,YACF;AAEA,4BAAgB,IAAI,UAAU,IAAI;AAAA,UACpC;AACA,cAAI,UAAU,WAAW;AAEvB,gBAAI,KAAK,WAAW;AAClB,mBAAK,SAAS;AACd,mBAAK,YAAY;AACjB,mBAAK,UAAU,CAAC;AAChB,qBAAO,MAAM,GAAG;AAChB,mBAAK,KAAK;AAAA,YACZ;AAAA,UACF;AACA,sBAAY,GAAG;AAAA,QACjB;AAAA,MACF;AAMA,UAAI,cAAc,SAAS,KAAK;AAC9B,YAAI;AACF,cAAI,KAAK;AAAA,QACX,SAAS,GAAG;AACV,cAAI,QAAQ;AAAA,QACd;AAAA,MACF;AAOA,UAAI,kBAAkB,SAAS,aAAa,MAAM;AAEhD,YAAI,QAAQ,WAAW;AACrB,eAAK,MAAM,aAAa,MAAM,6BAA6B;AAAA,QAC7D;AAGA,YAAI,UAAU,SAAS,QAAQ;AAC7B,cAAI,UAAU,KAAK,QAAQ,SAAS,GAAG;AACrC,kBAAM,KAAK,IAAI,IAAI;AACnB,sBAAU,MAAM,MAAM;AAAA,UACxB,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAGA,YAAI,OAAO,YAAY,eAAeA,QAAO,IAAI,gBAAgB,WAAW,GAAG;AAC7E,UAAAA,QAAO,IAAI,gBAAgB,WAAW,EAAE,KAAK,OAAO,EAAE,MAAM,KAAK;AAAA,QACnE,OAAO;AACL,UAAAA,QAAO,IAAI,gBAAgB,aAAa,SAAS,KAAK;AAAA,QACxD;AAAA,MACF;AAOA,UAAI,YAAY,SAAS,MAAM,QAAQ;AAErC,YAAI,UAAU,CAAC,KAAK,WAAW;AAC7B,eAAK,YAAY,OAAO;AAAA,QAC1B;AAGA,YAAI,OAAO,KAAK,KAAK,OAAO,EAAE,WAAW,GAAG;AAC1C,eAAK,UAAU,EAAC,WAAW,CAAC,GAAG,KAAK,YAAY,GAAI,EAAC;AAAA,QACvD;AAGA,YAAI,KAAK,WAAW,UAAU;AAC5B,eAAK,SAAS;AACd,eAAK,MAAM,MAAM;AACjB,eAAK,WAAW;AAAA,QAClB;AAAA,MACF;AAKA,UAAI,oBAAoB,WAAW;AAEjC,YAAI,CAACA,QAAO,eAAe;AACzB;AAAA,QACF;AAGA,YAAI;AACF,cAAI,OAAO,iBAAiB,aAAa;AACvC,YAAAA,QAAO,MAAM,IAAI,aAAa;AAAA,UAChC,WAAW,OAAO,uBAAuB,aAAa;AACpD,YAAAA,QAAO,MAAM,IAAI,mBAAmB;AAAA,UACtC,OAAO;AACL,YAAAA,QAAO,gBAAgB;AAAA,UACzB;AAAA,QACF,SAAQ,GAAG;AACT,UAAAA,QAAO,gBAAgB;AAAA,QACzB;AAGA,YAAI,CAACA,QAAO,KAAK;AACf,UAAAA,QAAO,gBAAgB;AAAA,QACzB;AAIA,YAAI,MAAO,iBAAiB,KAAKA,QAAO,cAAcA,QAAO,WAAW,QAAQ;AAChF,YAAI,aAAaA,QAAO,cAAcA,QAAO,WAAW,WAAW,MAAM,wBAAwB;AACjG,YAAI,UAAU,aAAa,SAAS,WAAW,CAAC,GAAG,EAAE,IAAI;AACzD,YAAI,OAAO,WAAW,UAAU,GAAG;AACjC,cAAI,SAAS,SAAS,KAAKA,QAAO,cAAcA,QAAO,WAAW,UAAU,YAAY,CAAC;AACzF,cAAIA,QAAO,cAAc,CAAC,QAAQ;AAChC,YAAAA,QAAO,gBAAgB;AAAA,UACzB;AAAA,QACF;AAGA,YAAIA,QAAO,eAAe;AACxB,UAAAA,QAAO,aAAc,OAAOA,QAAO,IAAI,eAAe,cAAeA,QAAO,IAAI,eAAe,IAAIA,QAAO,IAAI,WAAW;AACzH,UAAAA,QAAO,WAAW,KAAK,eAAeA,QAAO,SAAS,IAAIA,QAAO,SAASA,QAAO,IAAI,WAAW;AAChG,UAAAA,QAAO,WAAW,QAAQA,QAAO,IAAI,WAAW;AAAA,QAClD;AAGA,QAAAA,QAAO,OAAO;AAAA,MAChB;AAGA,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC9C,eAAO,CAAC,GAAG,WAAW;AACpB,iBAAO;AAAA,YACL,QAAQA;AAAA,YACR,MAAMG;AAAA,UACR;AAAA,QACF,CAAC;AAAA,MACH;AAGA,UAAI,OAAO,YAAY,aAAa;AAClC,gBAAQ,SAASH;AACjB,gBAAQ,OAAOG;AAAA,MACjB;AAGA,UAAI,OAAO,WAAW,aAAa;AACjC,eAAO,eAAeJ;AACtB,eAAO,SAASC;AAChB,eAAO,OAAOG;AACd,eAAO,QAAQC;AAAA,MACjB,WAAW,OAAO,WAAW,aAAa;AACxC,eAAO,eAAeL;AACtB,eAAO,SAASC;AAChB,eAAO,OAAOG;AACd,eAAO,QAAQC;AAAA,MACjB;AAAA,IACF,GAAG;AAeH,KAAC,WAAW;AAEV;AAGA,mBAAa,UAAU,OAAO,CAAC,GAAG,GAAG,CAAC;AACtC,mBAAa,UAAU,eAAe,CAAC,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC;AAWxD,mBAAa,UAAU,SAAS,SAAS,KAAK;AAC5C,YAAI,OAAO;AAGX,YAAI,CAAC,KAAK,OAAO,CAAC,KAAK,IAAI,UAAU;AACnC,iBAAO;AAAA,QACT;AAGA,iBAAS,IAAE,KAAK,OAAO,SAAO,GAAG,KAAG,GAAG,KAAK;AAC1C,eAAK,OAAO,CAAC,EAAE,OAAO,GAAG;AAAA,QAC3B;AAEA,eAAO;AAAA,MACT;AAUA,mBAAa,UAAU,MAAM,SAAS,GAAG,GAAG,GAAG;AAC7C,YAAI,OAAO;AAGX,YAAI,CAAC,KAAK,OAAO,CAAC,KAAK,IAAI,UAAU;AACnC,iBAAO;AAAA,QACT;AAGA,YAAK,OAAO,MAAM,WAAY,KAAK,KAAK,CAAC,IAAI;AAC7C,YAAK,OAAO,MAAM,WAAY,KAAK,KAAK,CAAC,IAAI;AAE7C,YAAI,OAAO,MAAM,UAAU;AACzB,eAAK,OAAO,CAAC,GAAG,GAAG,CAAC;AAEpB,cAAI,OAAO,KAAK,IAAI,SAAS,cAAc,aAAa;AACtD,iBAAK,IAAI,SAAS,UAAU,gBAAgB,KAAK,KAAK,CAAC,GAAG,OAAO,IAAI,aAAa,GAAG;AACrF,iBAAK,IAAI,SAAS,UAAU,gBAAgB,KAAK,KAAK,CAAC,GAAG,OAAO,IAAI,aAAa,GAAG;AACrF,iBAAK,IAAI,SAAS,UAAU,gBAAgB,KAAK,KAAK,CAAC,GAAG,OAAO,IAAI,aAAa,GAAG;AAAA,UACvF,OAAO;AACL,iBAAK,IAAI,SAAS,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC;AAAA,UACxE;AAAA,QACF,OAAO;AACL,iBAAO,KAAK;AAAA,QACd;AAEA,eAAO;AAAA,MACT;AAgBA,mBAAa,UAAU,cAAc,SAAS,GAAG,GAAG,GAAG,KAAK,KAAK,KAAK;AACpE,YAAI,OAAO;AAGX,YAAI,CAAC,KAAK,OAAO,CAAC,KAAK,IAAI,UAAU;AACnC,iBAAO;AAAA,QACT;AAGA,YAAI,KAAK,KAAK;AACd,YAAK,OAAO,MAAM,WAAY,GAAG,CAAC,IAAI;AACtC,YAAK,OAAO,MAAM,WAAY,GAAG,CAAC,IAAI;AACtC,cAAO,OAAO,QAAQ,WAAY,GAAG,CAAC,IAAI;AAC1C,cAAO,OAAO,QAAQ,WAAY,GAAG,CAAC,IAAI;AAC1C,cAAO,OAAO,QAAQ,WAAY,GAAG,CAAC,IAAI;AAE1C,YAAI,OAAO,MAAM,UAAU;AACzB,eAAK,eAAe,CAAC,GAAG,GAAG,GAAG,KAAK,KAAK,GAAG;AAE3C,cAAI,OAAO,KAAK,IAAI,SAAS,aAAa,aAAa;AACrD,iBAAK,IAAI,SAAS,SAAS,gBAAgB,GAAG,OAAO,IAAI,aAAa,GAAG;AACzE,iBAAK,IAAI,SAAS,SAAS,gBAAgB,GAAG,OAAO,IAAI,aAAa,GAAG;AACzE,iBAAK,IAAI,SAAS,SAAS,gBAAgB,GAAG,OAAO,IAAI,aAAa,GAAG;AACzE,iBAAK,IAAI,SAAS,IAAI,gBAAgB,KAAK,OAAO,IAAI,aAAa,GAAG;AACtE,iBAAK,IAAI,SAAS,IAAI,gBAAgB,KAAK,OAAO,IAAI,aAAa,GAAG;AACtE,iBAAK,IAAI,SAAS,IAAI,gBAAgB,KAAK,OAAO,IAAI,aAAa,GAAG;AAAA,UACxE,OAAO;AACL,iBAAK,IAAI,SAAS,eAAe,GAAG,GAAG,GAAG,KAAK,KAAK,GAAG;AAAA,UACzD;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAUA,WAAK,UAAU,OAAQ,yBAAS,QAAQ;AACtC,eAAO,SAAS,GAAG;AACjB,cAAI,OAAO;AAGX,eAAK,eAAe,EAAE,eAAe,CAAC,GAAG,GAAG,CAAC;AAC7C,eAAK,UAAU,EAAE,UAAU;AAC3B,eAAK,OAAO,EAAE,OAAO;AACrB,eAAK,cAAc;AAAA,YACjB,gBAAgB,OAAO,EAAE,mBAAmB,cAAc,EAAE,iBAAiB;AAAA,YAC7E,gBAAgB,OAAO,EAAE,mBAAmB,cAAc,EAAE,iBAAiB;AAAA,YAC7E,eAAe,OAAO,EAAE,kBAAkB,cAAc,EAAE,gBAAgB;AAAA,YAC1E,eAAe,OAAO,EAAE,kBAAkB,cAAc,EAAE,gBAAgB;AAAA,YAC1E,aAAa,OAAO,EAAE,gBAAgB,cAAc,EAAE,cAAc;AAAA,YACpE,cAAc,OAAO,EAAE,iBAAiB,cAAc,EAAE,eAAe;AAAA,YACvE,aAAa,OAAO,EAAE,gBAAgB,cAAc,EAAE,cAAc;AAAA,YACpE,eAAe,OAAO,EAAE,kBAAkB,cAAc,EAAE,gBAAgB;AAAA,UAC5E;AAGA,eAAK,YAAY,EAAE,WAAW,CAAC,EAAC,IAAI,EAAE,SAAQ,CAAC,IAAI,CAAC;AACpD,eAAK,SAAS,EAAE,QAAQ,CAAC,EAAC,IAAI,EAAE,MAAK,CAAC,IAAI,CAAC;AAC3C,eAAK,iBAAiB,EAAE,gBAAgB,CAAC,EAAC,IAAI,EAAE,cAAa,CAAC,IAAI,CAAC;AAGnE,iBAAO,OAAO,KAAK,MAAM,CAAC;AAAA,QAC5B;AAAA,MACF,EAAG,KAAK,UAAU,IAAI;AAQtB,WAAK,UAAU,SAAS,SAAS,KAAK,IAAI;AACxC,YAAI,OAAO;AAGX,YAAI,CAAC,KAAK,WAAW;AACnB,iBAAO;AAAA,QACT;AAGA,YAAI,KAAK,WAAW,UAAU;AAC5B,eAAK,OAAO,KAAK;AAAA,YACf,OAAO;AAAA,YACP,QAAQ,WAAW;AACjB,mBAAK,OAAO,KAAK,EAAE;AAAA,YACrB;AAAA,UACF,CAAC;AAED,iBAAO;AAAA,QACT;AAGA,YAAI,aAAc,OAAO,OAAO,IAAI,uBAAuB,cAAe,YAAY;AAGtF,YAAI,OAAO,OAAO,aAAa;AAE7B,cAAI,OAAO,QAAQ,UAAU;AAC3B,iBAAK,UAAU;AACf,iBAAK,OAAO,CAAC,KAAK,GAAG,CAAC;AAAA,UACxB,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAGA,YAAI,MAAM,KAAK,aAAa,EAAE;AAC9B,iBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAE/B,cAAI,QAAQ,KAAK,WAAW,IAAI,CAAC,CAAC;AAElC,cAAI,OAAO;AACT,gBAAI,OAAO,QAAQ,UAAU;AAC3B,oBAAM,UAAU;AAChB,oBAAM,OAAO,CAAC,KAAK,GAAG,CAAC;AAEvB,kBAAI,MAAM,OAAO;AAEf,sBAAM,YAAY,eAAe;AAGjC,oBAAI,CAAC,MAAM,WAAW,CAAC,MAAM,QAAQ,KAAK;AACxC,8BAAY,OAAO,UAAU;AAAA,gBAC/B;AAEA,oBAAI,eAAe,WAAW;AAC5B,sBAAI,OAAO,MAAM,QAAQ,cAAc,aAAa;AAClD,0BAAM,QAAQ,UAAU,eAAe,KAAK,OAAO,IAAI,WAAW;AAClE,0BAAM,QAAQ,UAAU,eAAe,GAAG,OAAO,IAAI,WAAW;AAChE,0BAAM,QAAQ,UAAU,eAAe,GAAG,OAAO,IAAI,WAAW;AAAA,kBAClE,OAAO;AACL,0BAAM,QAAQ,YAAY,KAAK,GAAG,CAAC;AAAA,kBACrC;AAAA,gBACF,OAAO;AACL,wBAAM,QAAQ,IAAI,eAAe,KAAK,OAAO,IAAI,WAAW;AAAA,gBAC9D;AAAA,cACF;AAEA,mBAAK,MAAM,UAAU,MAAM,GAAG;AAAA,YAChC,OAAO;AACL,qBAAO,MAAM;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAUA,WAAK,UAAU,MAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AACzC,YAAI,OAAO;AAGX,YAAI,CAAC,KAAK,WAAW;AACnB,iBAAO;AAAA,QACT;AAGA,YAAI,KAAK,WAAW,UAAU;AAC5B,eAAK,OAAO,KAAK;AAAA,YACf,OAAO;AAAA,YACP,QAAQ,WAAW;AACjB,mBAAK,IAAI,GAAG,GAAG,GAAG,EAAE;AAAA,YACtB;AAAA,UACF,CAAC;AAED,iBAAO;AAAA,QACT;AAGA,YAAK,OAAO,MAAM,WAAY,IAAI;AAClC,YAAK,OAAO,MAAM,WAAY,OAAO;AAGrC,YAAI,OAAO,OAAO,aAAa;AAE7B,cAAI,OAAO,MAAM,UAAU;AACzB,iBAAK,OAAO,CAAC,GAAG,GAAG,CAAC;AAAA,UACtB,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAGA,YAAI,MAAM,KAAK,aAAa,EAAE;AAC9B,iBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAE/B,cAAI,QAAQ,KAAK,WAAW,IAAI,CAAC,CAAC;AAElC,cAAI,OAAO;AACT,gBAAI,OAAO,MAAM,UAAU;AACzB,oBAAM,OAAO,CAAC,GAAG,GAAG,CAAC;AAErB,kBAAI,MAAM,OAAO;AAEf,oBAAI,CAAC,MAAM,WAAW,MAAM,QAAQ,KAAK;AACvC,8BAAY,OAAO,SAAS;AAAA,gBAC9B;AAEA,oBAAI,OAAO,MAAM,QAAQ,cAAc,aAAa;AAClD,wBAAM,QAAQ,UAAU,eAAe,GAAG,OAAO,IAAI,WAAW;AAChE,wBAAM,QAAQ,UAAU,eAAe,GAAG,OAAO,IAAI,WAAW;AAChE,wBAAM,QAAQ,UAAU,eAAe,GAAG,OAAO,IAAI,WAAW;AAAA,gBAClE,OAAO;AACL,wBAAM,QAAQ,YAAY,GAAG,GAAG,CAAC;AAAA,gBACnC;AAAA,cACF;AAEA,mBAAK,MAAM,OAAO,MAAM,GAAG;AAAA,YAC7B,OAAO;AACL,qBAAO,MAAM;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAYA,WAAK,UAAU,cAAc,SAAS,GAAG,GAAG,GAAG,IAAI;AACjD,YAAI,OAAO;AAGX,YAAI,CAAC,KAAK,WAAW;AACnB,iBAAO;AAAA,QACT;AAGA,YAAI,KAAK,WAAW,UAAU;AAC5B,eAAK,OAAO,KAAK;AAAA,YACf,OAAO;AAAA,YACP,QAAQ,WAAW;AACjB,mBAAK,YAAY,GAAG,GAAG,GAAG,EAAE;AAAA,YAC9B;AAAA,UACF,CAAC;AAED,iBAAO;AAAA,QACT;AAGA,YAAK,OAAO,MAAM,WAAY,KAAK,aAAa,CAAC,IAAI;AACrD,YAAK,OAAO,MAAM,WAAY,KAAK,aAAa,CAAC,IAAI;AAGrD,YAAI,OAAO,OAAO,aAAa;AAE7B,cAAI,OAAO,MAAM,UAAU;AACzB,iBAAK,eAAe,CAAC,GAAG,GAAG,CAAC;AAAA,UAC9B,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAGA,YAAI,MAAM,KAAK,aAAa,EAAE;AAC9B,iBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAE/B,cAAI,QAAQ,KAAK,WAAW,IAAI,CAAC,CAAC;AAElC,cAAI,OAAO;AACT,gBAAI,OAAO,MAAM,UAAU;AACzB,oBAAM,eAAe,CAAC,GAAG,GAAG,CAAC;AAE7B,kBAAI,MAAM,OAAO;AAEf,oBAAI,CAAC,MAAM,SAAS;AAElB,sBAAI,CAAC,MAAM,MAAM;AACf,0BAAM,OAAO,KAAK,QAAQ,CAAC,GAAG,GAAG,IAAI;AAAA,kBACvC;AAEA,8BAAY,OAAO,SAAS;AAAA,gBAC9B;AAEA,oBAAI,OAAO,MAAM,QAAQ,iBAAiB,aAAa;AACrD,wBAAM,QAAQ,aAAa,eAAe,GAAG,OAAO,IAAI,WAAW;AACnE,wBAAM,QAAQ,aAAa,eAAe,GAAG,OAAO,IAAI,WAAW;AACnE,wBAAM,QAAQ,aAAa,eAAe,GAAG,OAAO,IAAI,WAAW;AAAA,gBACrE,OAAO;AACL,wBAAM,QAAQ,eAAe,GAAG,GAAG,CAAC;AAAA,gBACtC;AAAA,cACF;AAEA,mBAAK,MAAM,eAAe,MAAM,GAAG;AAAA,YACrC,OAAO;AACL,qBAAO,MAAM;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAgCA,WAAK,UAAU,aAAa,WAAW;AACrC,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,GAAG,IAAI;AAGX,YAAI,CAAC,KAAK,WAAW;AACnB,iBAAO;AAAA,QACT;AAGA,YAAI,KAAK,WAAW,GAAG;AAErB,iBAAO,KAAK;AAAA,QACd,WAAW,KAAK,WAAW,GAAG;AAC5B,cAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAC/B,gBAAI,KAAK,CAAC;AAGV,gBAAI,OAAO,OAAO,aAAa;AAC7B,kBAAI,CAAC,EAAE,YAAY;AACjB,kBAAE,aAAa;AAAA,kBACb,gBAAgB,EAAE;AAAA,kBAClB,gBAAgB,EAAE;AAAA,kBAClB,eAAe,EAAE;AAAA,kBACjB,eAAe,EAAE;AAAA,kBACjB,aAAa,EAAE;AAAA,kBACf,aAAa,EAAE;AAAA,kBACf,eAAe,EAAE;AAAA,kBACjB,cAAc,EAAE;AAAA,gBAClB;AAAA,cACF;AAEA,mBAAK,cAAc;AAAA,gBACjB,gBAAgB,OAAO,EAAE,WAAW,mBAAmB,cAAc,EAAE,WAAW,iBAAiB,KAAK;AAAA,gBACxG,gBAAgB,OAAO,EAAE,WAAW,mBAAmB,cAAc,EAAE,WAAW,iBAAiB,KAAK;AAAA,gBACxG,eAAe,OAAO,EAAE,WAAW,kBAAkB,cAAc,EAAE,WAAW,gBAAgB,KAAK;AAAA,gBACrG,eAAe,OAAO,EAAE,WAAW,kBAAkB,cAAc,EAAE,WAAW,gBAAgB,KAAK;AAAA,gBACrG,aAAa,OAAO,EAAE,WAAW,gBAAgB,cAAc,EAAE,WAAW,cAAc,KAAK;AAAA,gBAC/F,aAAa,OAAO,EAAE,WAAW,gBAAgB,cAAc,EAAE,WAAW,cAAc,KAAK;AAAA,gBAC/F,eAAe,OAAO,EAAE,WAAW,kBAAkB,cAAc,EAAE,WAAW,gBAAgB,KAAK;AAAA,gBACrG,cAAc,OAAO,EAAE,WAAW,iBAAiB,cAAc,EAAE,WAAW,eAAe,KAAK;AAAA,cACpG;AAAA,YACF;AAAA,UACF,OAAO;AAEL,oBAAQ,KAAK,WAAW,SAAS,KAAK,CAAC,GAAG,EAAE,CAAC;AAC7C,mBAAO,QAAQ,MAAM,cAAc,KAAK;AAAA,UAC1C;AAAA,QACF,WAAW,KAAK,WAAW,GAAG;AAC5B,cAAI,KAAK,CAAC;AACV,eAAK,SAAS,KAAK,CAAC,GAAG,EAAE;AAAA,QAC3B;AAGA,YAAI,MAAM,KAAK,aAAa,EAAE;AAC9B,iBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ,KAAK;AAC/B,kBAAQ,KAAK,WAAW,IAAI,CAAC,CAAC;AAE9B,cAAI,OAAO;AAET,gBAAI,KAAK,MAAM;AACf,iBAAK;AAAA,cACH,gBAAgB,OAAO,EAAE,mBAAmB,cAAc,EAAE,iBAAiB,GAAG;AAAA,cAChF,gBAAgB,OAAO,EAAE,mBAAmB,cAAc,EAAE,iBAAiB,GAAG;AAAA,cAChF,eAAe,OAAO,EAAE,kBAAkB,cAAc,EAAE,gBAAgB,GAAG;AAAA,cAC7E,eAAe,OAAO,EAAE,kBAAkB,cAAc,EAAE,gBAAgB,GAAG;AAAA,cAC7E,aAAa,OAAO,EAAE,gBAAgB,cAAc,EAAE,cAAc,GAAG;AAAA,cACvE,aAAa,OAAO,EAAE,gBAAgB,cAAc,EAAE,cAAc,GAAG;AAAA,cACvE,eAAe,OAAO,EAAE,kBAAkB,cAAc,EAAE,gBAAgB,GAAG;AAAA,cAC7E,cAAc,OAAO,EAAE,iBAAiB,cAAc,EAAE,eAAe,GAAG;AAAA,YAC5E;AAGA,gBAAI,SAAS,MAAM;AACnB,gBAAI,CAAC,QAAQ;AAEX,kBAAI,CAAC,MAAM,MAAM;AACf,sBAAM,OAAO,KAAK,QAAQ,CAAC,GAAG,GAAG,IAAI;AAAA,cACvC;AAGA,0BAAY,OAAO,SAAS;AAC5B,uBAAS,MAAM;AAAA,YACjB;AAGA,mBAAO,iBAAiB,GAAG;AAC3B,mBAAO,iBAAiB,GAAG;AAC3B,mBAAO,gBAAgB,GAAG;AAC1B,mBAAO,gBAAgB,GAAG;AAC1B,mBAAO,cAAc,GAAG;AACxB,mBAAO,cAAc,GAAG;AACxB,mBAAO,gBAAgB,GAAG;AAC1B,mBAAO,eAAe,GAAG;AAAA,UAC3B;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAUA,YAAM,UAAU,OAAQ,yBAAS,QAAQ;AACvC,eAAO,WAAW;AAChB,cAAI,OAAO;AACX,cAAI,SAAS,KAAK;AAGlB,eAAK,eAAe,OAAO;AAC3B,eAAK,UAAU,OAAO;AACtB,eAAK,OAAO,OAAO;AACnB,eAAK,cAAc,OAAO;AAG1B,iBAAO,KAAK,IAAI;AAGhB,cAAI,KAAK,SAAS;AAChB,mBAAO,OAAO,KAAK,OAAO;AAAA,UAC5B,WAAW,KAAK,MAAM;AACpB,mBAAO,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG;AAAA,UAC/D;AAAA,QACF;AAAA,MACF,EAAG,MAAM,UAAU,IAAI;AAOvB,YAAM,UAAU,QAAS,yBAAS,QAAQ;AACxC,eAAO,WAAW;AAChB,cAAI,OAAO;AACX,cAAI,SAAS,KAAK;AAGlB,eAAK,eAAe,OAAO;AAC3B,eAAK,UAAU,OAAO;AACtB,eAAK,OAAO,OAAO;AACnB,eAAK,cAAc,OAAO;AAG1B,cAAI,KAAK,SAAS;AAChB,mBAAO,OAAO,KAAK,OAAO;AAAA,UAC5B,WAAW,KAAK,MAAM;AACpB,mBAAO,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG;AAAA,UAC/D,WAAW,KAAK,SAAS;AAEvB,iBAAK,QAAQ,WAAW,CAAC;AACzB,iBAAK,UAAU;AACf,mBAAO,eAAe,IAAI;AAAA,UAC5B;AAGA,iBAAO,OAAO,KAAK,IAAI;AAAA,QACzB;AAAA,MACF,EAAG,MAAM,UAAU,KAAK;AAUxB,UAAI,cAAc,SAAS,OAAO,MAAM;AACtC,eAAO,QAAQ;AAGf,YAAI,SAAS,WAAW;AACtB,gBAAM,UAAU,OAAO,IAAI,aAAa;AACxC,gBAAM,QAAQ,iBAAiB,MAAM,YAAY;AACjD,gBAAM,QAAQ,iBAAiB,MAAM,YAAY;AACjD,gBAAM,QAAQ,gBAAgB,MAAM,YAAY;AAChD,gBAAM,QAAQ,gBAAgB,MAAM,YAAY;AAChD,gBAAM,QAAQ,cAAc,MAAM,YAAY;AAC9C,gBAAM,QAAQ,cAAc,MAAM,YAAY;AAC9C,gBAAM,QAAQ,gBAAgB,MAAM,YAAY;AAChD,gBAAM,QAAQ,eAAe,MAAM,YAAY;AAE/C,cAAI,OAAO,MAAM,QAAQ,cAAc,aAAa;AAClD,kBAAM,QAAQ,UAAU,eAAe,MAAM,KAAK,CAAC,GAAG,OAAO,IAAI,WAAW;AAC5E,kBAAM,QAAQ,UAAU,eAAe,MAAM,KAAK,CAAC,GAAG,OAAO,IAAI,WAAW;AAC5E,kBAAM,QAAQ,UAAU,eAAe,MAAM,KAAK,CAAC,GAAG,OAAO,IAAI,WAAW;AAAA,UAC9E,OAAO;AACL,kBAAM,QAAQ,YAAY,MAAM,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,CAAC;AAAA,UACvE;AAEA,cAAI,OAAO,MAAM,QAAQ,iBAAiB,aAAa;AACrD,kBAAM,QAAQ,aAAa,eAAe,MAAM,aAAa,CAAC,GAAG,OAAO,IAAI,WAAW;AACvF,kBAAM,QAAQ,aAAa,eAAe,MAAM,aAAa,CAAC,GAAG,OAAO,IAAI,WAAW;AACvF,kBAAM,QAAQ,aAAa,eAAe,MAAM,aAAa,CAAC,GAAG,OAAO,IAAI,WAAW;AAAA,UACzF,OAAO;AACL,kBAAM,QAAQ,eAAe,MAAM,aAAa,CAAC,GAAG,MAAM,aAAa,CAAC,GAAG,MAAM,aAAa,CAAC,CAAC;AAAA,UAClG;AAAA,QACF,OAAO;AACL,gBAAM,UAAU,OAAO,IAAI,mBAAmB;AAC9C,gBAAM,QAAQ,IAAI,eAAe,MAAM,SAAS,OAAO,IAAI,WAAW;AAAA,QACxE;AAEA,cAAM,QAAQ,QAAQ,MAAM,KAAK;AAGjC,YAAI,CAAC,MAAM,SAAS;AAClB,gBAAM,QAAQ,MAAM,MAAM,KAAK,IAAI,EAAE,KAAK,MAAM,KAAK,IAAI;AAAA,QAC3D;AAAA,MACF;AAAA,IACF,GAAG;AAAA;AAAA;", "names": ["HowlerGlobal", "Howler", "e", "i", "Howl", "Sound"]}