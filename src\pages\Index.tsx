import { useState, useCallback } from 'react';
import { Symbol, Alert, SymbolConfig } from '@/types/market';
import { TickerGrid } from '@/components/TickerGrid';
import { AlertPanel } from '@/components/AlertPanel';
import { DetailsPanel } from '@/components/DetailsPanel';
import { useWebSocket } from '@/hooks/useWebSocket';
import { useAudioAlerts } from '@/hooks/useAudioAlerts';
import { Badge } from '@/components/ui/badge';
import { Activity } from 'lucide-react';
import { SYMBOLS } from '@/lib/symbols';

const Index = () => {
  const [selectedSymbol, setSelectedSymbol] = useState<Symbol>(SYMBOLS[0]);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [activeAlertStates, setActiveAlertStates] = useState<Record<string, boolean>>({});
  const [configs, setConfigs] = useState<Record<Symbol, SymbolConfig>>(() => {
    return SYMBOLS.reduce((acc, symbol) => {
      acc[symbol] = {
        symbol,
        spreadThreshold: 1,
        deviationThreshold: 1,
      };
      return acc;
    }, {} as Record<Symbol, SymbolConfig>);
  });

  const { triggerAlert, clearAlert } = useAudioAlerts();

  const handleAlertTriggered = useCallback((alert: Alert) => {
    const alertKey = `${alert.symbol}-${alert.alertType}`;
    
    // Only add to alert list and trigger audio if this is a new alert
    setActiveAlertStates(prev => {
      if (!prev[alertKey]) {
        setAlerts(prevAlerts => [alert, ...prevAlerts]);
        triggerAlert(alert.symbol, alert.alertType);
        return { ...prev, [alertKey]: true };
      }
      return prev;
    });
  }, [triggerAlert]);

  const handleAlertCleared = useCallback((symbol: Symbol, alertType: string, value: number, threshold: number) => {
    const alertKey = `${symbol}-${alertType}`;
    setActiveAlertStates(prev => {
      if (prev[alertKey]) {
        // Add a "cleared" alert to the history
        const clearedAlert: Alert = {
          id: `${Date.now()}-${symbol}-${alertType}-CLEARED`,
          timestamp: new Date(),
          symbol,
          alertType: alertType as any,
          value,
          threshold,
          status: 'cleared',
          prices: {},
        };
        setAlerts(prevAlerts => [clearedAlert, ...prevAlerts]);
        
        clearAlert(symbol, alertType as any);
        return { ...prev, [alertKey]: false };
      }
      return prev;
    });
  }, [clearAlert]);

  const { priceData, isConnected } = useWebSocket(handleAlertTriggered, handleAlertCleared, configs);

  const handleClearAllAlerts = () => {
    setAlerts([]);
  };

  const handleConfigUpdate = (config: SymbolConfig) => {
    setConfigs(prev => ({
      ...prev,
      [config.symbol]: config,
    }));
  };

  // Filter to show only currently active alerts based on state
  const activeAlerts = alerts.filter(alert => {
    const alertKey = `${alert.symbol}-${alert.alertType}`;
    return activeAlertStates[alertKey];
  });

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-[1800px] mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight mb-1">
              Real-time Price Alert Console
            </h1>
            <p className="text-muted-foreground">
              Professional broker monitoring dashboard
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={isConnected ? "default" : "destructive"} className="gap-2">
              <Activity className="w-3 h-3" />
              {isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
          </div>
        </div>

        {/* Main Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left: Ticker Grid (spans 2 columns) */}
          <div className="lg:col-span-2 space-y-6">
            <TickerGrid
              priceData={priceData}
              activeAlerts={activeAlerts}
              onSymbolSelect={setSelectedSymbol}
              selectedSymbol={selectedSymbol}
            />
            
            {/* Details Panel - Below grid on desktop */}
            <DetailsPanel
              selectedSymbol={selectedSymbol}
              symbols={SYMBOLS}
              config={configs[selectedSymbol]}
              onSymbolChange={setSelectedSymbol}
              onConfigUpdate={handleConfigUpdate}
            />
          </div>

          {/* Right: Alert Panel */}
          <div className="lg:col-span-1">
            <AlertPanel
              alerts={alerts}
              onClearAll={handleClearAllAlerts}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
