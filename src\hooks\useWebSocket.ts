﻿import { useState, useEffect, useCallback, useRef } from 'react';
import { PriceData, Alert, Symbol, WebSocketEvent, SymbolConfig } from '@/types/market';
import { SYMBOLS } from '@/lib/symbols';

const getBasePrice = (symbol: Symbol) => {
  switch (symbol) {
    case 'LLG':
      return 100;
    case 'LLS':
      return 25;
    case 'HKG':
      return 50;
    case 'RKG':
    default:
      return 75;
  }
};

const createInitialEntry = (symbol: Symbol): PriceData => {
  const base = getBasePrice(symbol);
  const spread = 0.03;
  const ctrlOffset = 0.004;
  return {
    symbol,
    ourBid: base,
    ourAsk: base + spread,
    ctrlBid: base + ctrlOffset,
    ctrlAsk: base + spread + ctrlOffset,
    spread,
    deviation: ctrlOffset,
  };
};

const createInitialData = (): Record<Symbol, PriceData> => {
  const initial = {} as Record<Symbol, PriceData>;
  SYMBOLS.forEach(symbol => {
    initial[symbol] = createInitialEntry(symbol);
  });
  return initial;
};

const createTimerMap = () =>
  SYMBOLS.reduce(
    (acc, symbol) => {
      acc[symbol] = undefined;
      return acc;
    },
    {} as Record<Symbol, ReturnType<typeof setTimeout> | undefined>,
  );

export const useWebSocket = (
  onAlertTriggered: (alert: Alert) => void, 
  onAlertCleared: (symbol: Symbol, alertType: string, value: number, threshold: number) => void,
  configs: Record<Symbol, SymbolConfig>
) => {
  const [priceData, setPriceData] = useState<Record<Symbol, PriceData>>(() => createInitialData());

  const [isConnected, setIsConnected] = useState(false);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<ReturnType<typeof setTimeout>>();
  const symbolTickRefs = useRef<Record<Symbol, ReturnType<typeof setTimeout> | undefined>>(createTimerMap());
  const previousAlertStates = useRef<Record<string, boolean>>({});
  const latestPriceDataRef = useRef<Record<Symbol, PriceData>>(priceData);

  useEffect(() => {
    latestPriceDataRef.current = priceData;
  }, [priceData]);

  const checkAndTriggerAlerts = useCallback((symbol: Symbol, data: PriceData) => {
    const config = configs[symbol];
    if (!config) return;

    // Check Spread Alert
    const spreadKey = `${symbol}-SPREAD`;
    const isSpreadAlertActive = data.spread > config.spreadThreshold;
    
    if (isSpreadAlertActive && !previousAlertStates.current[spreadKey]) {
      console.log(`[Alert] Spread for ${symbol}: ${data.spread.toFixed(3)} > ${config.spreadThreshold.toFixed(3)}`);
      onAlertTriggered({
        id: `${Date.now()}-${symbol}-SPREAD`,
        timestamp: new Date(),
        symbol,
        alertType: 'SPREAD',
        value: data.spread,
        threshold: config.spreadThreshold,
        status: 'triggered',
        prices: {
          ourBid: data.ourBid,
          ourAsk: data.ourAsk,
        }
      });
      previousAlertStates.current[spreadKey] = true;
    } else if (!isSpreadAlertActive && previousAlertStates.current[spreadKey]) {
      console.log(`[Alert] Spread cleared for ${symbol}`);
      onAlertCleared(symbol, 'SPREAD', data.spread, config.spreadThreshold);
      previousAlertStates.current[spreadKey] = false;
    }

    // Check Deviation on Bid
    const deviationBid = Math.abs(data.ourBid - data.ctrlBid);
    const bidKey = `${symbol}-DEVIATION_BID`;
    const isBidAlertActive = deviationBid > config.deviationThreshold;
    
    if (isBidAlertActive && !previousAlertStates.current[bidKey]) {
      console.log(`[Alert] Bid deviation for ${symbol}: ${deviationBid.toFixed(3)} > ${config.deviationThreshold.toFixed(3)}`);
      onAlertTriggered({
        id: `${Date.now()}-${symbol}-DEVIATION_BID`,
        timestamp: new Date(),
        symbol,
        alertType: 'DEVIATION_BID',
        value: deviationBid,
        threshold: config.deviationThreshold,
        status: 'triggered',
        prices: {
          ourBid: data.ourBid,
          ctrlBid: data.ctrlBid,
        }
      });
      previousAlertStates.current[bidKey] = true;
    } else if (!isBidAlertActive && previousAlertStates.current[bidKey]) {
      console.log(`[Alert] Bid deviation cleared for ${symbol}`);
      onAlertCleared(symbol, 'DEVIATION_BID', deviationBid, config.deviationThreshold);
      previousAlertStates.current[bidKey] = false;
    }

    // Check Deviation on Ask
    const deviationAsk = Math.abs(data.ourAsk - data.ctrlAsk);
    const askKey = `${symbol}-DEVIATION_ASK`;
    const isAskAlertActive = deviationAsk > config.deviationThreshold;
    
    if (isAskAlertActive && !previousAlertStates.current[askKey]) {
      console.log(`[Alert] Ask deviation for ${symbol}: ${deviationAsk.toFixed(3)} > ${config.deviationThreshold.toFixed(3)}`);
      onAlertTriggered({
        id: `${Date.now()}-${symbol}-DEVIATION_ASK`,
        timestamp: new Date(),
        symbol,
        alertType: 'DEVIATION_ASK',
        value: deviationAsk,
        threshold: config.deviationThreshold,
        status: 'triggered',
        prices: {
          ourAsk: data.ourAsk,
          ctrlAsk: data.ctrlAsk,
        }
      });
      previousAlertStates.current[askKey] = true;
    } else if (!isAskAlertActive && previousAlertStates.current[askKey]) {
      console.log(`[Alert] Ask deviation cleared for ${symbol}`);
      onAlertCleared(symbol, 'DEVIATION_ASK', deviationAsk, config.deviationThreshold);
      previousAlertStates.current[askKey] = false;
    }
  }, [configs, onAlertTriggered, onAlertCleared]);

  const simulateSymbol = useCallback(
    (symbol: Symbol) => {
      setPriceData(prev => {
        const basePrice = getBasePrice(symbol);
        const prevData = prev[symbol] ?? createInitialEntry(symbol);

        const shouldExceedSpread = Math.random() > 0.88;
        const shouldExceedDeviation = Math.random() > 0.92;

        const targetSpread = shouldExceedSpread ? 0.045 + Math.random() * 0.01 : 0.015 + Math.random() * 0.004;
        const spread = prevData.spread + (targetSpread - prevData.spread) * 0.18;

        const driftTowardsBase = (basePrice - prevData.ourBid) * 0.03;
        const randomDrift = (Math.random() - 0.5) * 0.015;
        const ourBid = prevData.ourBid + driftTowardsBase + randomDrift;
        const ourAsk = ourBid + spread;

        const deviationStep =
          shouldExceedDeviation ? 0.005 + Math.random() * 0.004 : 0.0005 + Math.random() * 0.001;
        const ctrlTargetBid = ourBid + (Math.random() > 0.5 ? deviationStep : -deviationStep);
        const ctrlTargetAsk = ourAsk + (Math.random() > 0.5 ? deviationStep : -deviationStep);
        const ctrlBid = prevData.ctrlBid + (ctrlTargetBid - prevData.ctrlBid) * 0.25;
        const ctrlAsk = prevData.ctrlAsk + (ctrlTargetAsk - prevData.ctrlAsk) * 0.25;

        const deviation = Math.max(Math.abs(ourBid - ctrlBid), Math.abs(ourAsk - ctrlAsk));

        const newData: PriceData = {
          symbol,
          ourBid,
          ourAsk,
          ctrlBid,
          ctrlAsk,
          spread,
          deviation,
        };

        const updated = { ...prev, [symbol]: newData };
        latestPriceDataRef.current = updated;
        checkAndTriggerAlerts(symbol, newData);
        return updated;
      });
    },
    [checkAndTriggerAlerts],
  );

  const scheduleSymbolTick = useCallback(
    (symbol: Symbol, initialDelay?: number) => {
      const existingTimeout = symbolTickRefs.current[symbol];
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }

      const delay = initialDelay ?? Math.random() * 500;
      symbolTickRefs.current[symbol] = setTimeout(() => {
        symbolTickRefs.current[symbol] = undefined;
        simulateSymbol(symbol);
        scheduleSymbolTick(symbol);
      }, delay);
    },
    [simulateSymbol],
  );

  const connect = useCallback(() => {
    try {
      setIsConnected(true);
      console.log('[WebSocket] Connected (simulated)');

      SYMBOLS.forEach((symbol, index) => {
        const initialDelay = Math.random() * 300 ;
        scheduleSymbolTick(symbol, initialDelay);
      });

      return () => {
        SYMBOLS.forEach(symbol => {
          const timeoutId = symbolTickRefs.current[symbol];
          if (timeoutId) {
            clearTimeout(timeoutId);
            symbolTickRefs.current[symbol] = undefined;
          }
        });
        setIsConnected(false);
        console.log('[WebSocket] Disconnected');
      };
    } catch (error) {
      console.error('WebSocket connection error:', error);
      setIsConnected(false);

      SYMBOLS.forEach(symbol => {
        const timeoutId = symbolTickRefs.current[symbol];
        if (timeoutId) {
          clearTimeout(timeoutId);
          symbolTickRefs.current[symbol] = undefined;
        }
      });

      reconnectTimeoutRef.current = setTimeout(() => {
        connect();
      }, 500);
    }
  }, [scheduleSymbolTick]);

  useEffect(() => {
    const cleanup = connect();

    return () => {
      cleanup?.();
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = undefined;
      }
      SYMBOLS.forEach(symbol => {
        const timeoutId = symbolTickRefs.current[symbol];
        if (timeoutId) {
          clearTimeout(timeoutId);
          symbolTickRefs.current[symbol] = undefined;
        }
      });
      wsRef.current?.close();
    };
  }, [connect]);

  return { priceData, isConnected };
};








