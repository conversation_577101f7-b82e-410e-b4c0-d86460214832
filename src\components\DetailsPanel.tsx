import { useEffect, useState } from 'react';
import { Symbol, SymbolConfig } from '@/types/market';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface DetailsPanelProps {
  selectedSymbol: Symbol;
  symbols: Symbol[];
  config: SymbolConfig;
  onSymbolChange: (symbol: Symbol) => void;
  onConfigUpdate: (config: SymbolConfig) => void;
}

export const DetailsPanel = ({ selectedSymbol, symbols, config, onSymbolChange, onConfigUpdate }: DetailsPanelProps) => {
  const [spreadThreshold, setSpreadThreshold] = useState(config.spreadThreshold.toString());
  const [deviationThreshold, setDeviationThreshold] = useState(config.deviationThreshold.toString());

  useEffect(() => {
    setSpreadThreshold(config.spreadThreshold.toString());
    setDeviationThreshold(config.deviationThreshold.toString());
  }, [config, selectedSymbol]);

  const handleSave = async () => {
    const newConfig: SymbolConfig = {
      symbol: selectedSymbol,
      spreadThreshold: parseFloat(spreadThreshold),
      deviationThreshold: parseFloat(deviationThreshold),
    };

    try {
      // In production, this would call the API
      // await fetch('/api/config', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(newConfig),
      // });

      onConfigUpdate(newConfig);
      
      toast({
        title: "Configuration Updated",
        description: `Thresholds for ${selectedSymbol} have been saved.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update configuration.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="border border-border rounded-lg overflow-hidden h-full">
      <div className="bg-grid-header border-b border-border p-4">
        <div className="flex items-center justify-between gap-3">
          <h2 className="text-sm font-semibold uppercase tracking-wider">
            Symbol Configuration
          </h2>
          <div className="w-32">
            <Select value={selectedSymbol} onValueChange={(value) => onSymbolChange(value as Symbol)}>
              <SelectTrigger aria-label="Select symbol">
                <SelectValue placeholder="Select symbol" />
              </SelectTrigger>
              <SelectContent align="end">
                {symbols.map(symbol => (
                  <SelectItem key={symbol} value={symbol}>
                    {symbol}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
      
      <Tabs defaultValue="config" className="w-full">
        <TabsList className="w-full rounded-none border-b border-border bg-transparent p-0">
          <TabsTrigger 
            value="config" 
            className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
          >
            Configuration
          </TabsTrigger>
          <TabsTrigger 
            value="monitoring"
            className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
          >
            Monitoring
          </TabsTrigger>
        </TabsList>

        <TabsContent value="config" className="p-6 space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="spread-threshold">Spread Alert Threshold</Label>
              <Input
                id="spread-threshold"
                type="number"
                step="0.001"
                value={spreadThreshold}
                onChange={(e) => setSpreadThreshold(e.target.value)}
                className="font-mono-num"
              />
              <p className="text-xs text-muted-foreground">
                Alert when spread (Ask - Bid) exceeds this value
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="deviation-threshold">Deviation Alert Threshold</Label>
              <Input
                id="deviation-threshold"
                type="number"
                step="0.001"
                value={deviationThreshold}
                onChange={(e) => setDeviationThreshold(e.target.value)}
                className="font-mono-num"
              />
              <p className="text-xs text-muted-foreground">
                Alert when deviation from control feed exceeds this value
              </p>
            </div>

            <Button onClick={handleSave} className="w-full">
              Save Changes
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="monitoring" className="p-0">
          <div className="relative w-full" style={{ height: '600px' }}>
            <iframe
              src={`https://grafana.example.com/d/market-data?var-symbol=${selectedSymbol}`}
              className="w-full h-full border-0"
              title={`${selectedSymbol} Monitoring Dashboard`}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
